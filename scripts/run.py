#!/usr/bin/env python3
"""
股票数据管理系统启动脚本

直接运行此脚本来使用系统的各种功能。
支持从项目根目录或scripts目录运行。
"""

import sys
from pathlib import Path

def main():
    """主函数"""
    # 获取项目根目录（scripts的父目录）
    project_root = Path(__file__).parent.parent

    # 添加项目根目录到Python路径
    sys.path.insert(0, str(project_root))

    try:
        from src.cli import main as cli_main
        cli_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保在正确的目录下运行脚本，并且已安装所有依赖")
        sys.exit(1)
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
