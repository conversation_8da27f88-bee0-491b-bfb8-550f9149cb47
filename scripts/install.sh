#!/bin/bash
"""
股票数据管理系统安装脚本

自动化安装和配置过程。
"""

set -e  # 遇到错误立即退出

echo "🚀 股票数据管理系统安装脚本"
echo "================================"

# 检查conda是否安装
if ! command -v conda &> /dev/null; then
    echo "❌ 错误: 未找到conda命令"
    echo "请先安装Anaconda或Miniconda"
    echo "下载地址: https://docs.conda.io/en/latest/miniconda.html"
    exit 1
fi

echo "✅ 检测到conda环境"

# 检查环境是否已存在
if conda env list | grep -q "stock-db"; then
    echo "⚠️  环境 'stock-db' 已存在"
    read -p "是否要重新创建环境? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🗑️  删除现有环境..."
        conda env remove -n stock-db -y
    else
        echo "📦 使用现有环境"
        conda activate stock-db
        echo "✅ 环境已激活"
        exit 0
    fi
fi

# 创建conda环境
echo "📦 创建conda环境..."
conda env create -f environment.yml

echo "✅ 环境创建成功"

# 激活环境
echo "🔄 激活环境..."
eval "$(conda shell.bash hook)"
conda activate stock-db

# 检查环境变量文件
if [ ! -f ".env" ]; then
    echo "📝 创建环境变量文件..."
    cp .env.example .env
    echo "⚠️  请编辑 .env 文件配置数据库连接信息"
fi

# 检查数据库连接
echo "🔍 检查数据库连接..."
if python scripts/run.py check-db 2>/dev/null; then
    echo "✅ 数据库连接正常"

    # 初始化数据库
    echo "🗄️  初始化数据库..."
    python scripts/run.py init-db

    echo "📊 更新股票列表..."
    python scripts/run.py update-stocks

    echo "🎉 安装完成！"
    echo ""
    echo "快速开始:"
    echo "  conda activate stock-db"
    echo "  python scripts/run.py update-data --symbol 000001.SZ --periods 1d"
    echo "  python scripts/run.py query 000001.SZ --limit 5"
    
else
    echo "❌ 数据库连接失败"
    echo "请检查以下配置:"
    echo "1. PostgreSQL是否已安装并运行"
    echo "2. .env文件中的数据库配置是否正确"
    echo "3. 数据库用户是否有足够权限"
    echo ""
    echo "配置完成后运行:"
    echo "  conda activate stock-db"
    echo "  python scripts/run.py check-db"
    echo "  python scripts/run.py init-db"
fi

echo ""
echo "📚 更多信息请查看:"
echo "  - README.md"
echo "  - docs/quick_start.md"
echo "  - docs/deployment_guide.md"
