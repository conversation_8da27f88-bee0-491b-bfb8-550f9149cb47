#!/usr/bin/env python3
"""
每日数据更新脚本

可以通过cron定时执行的独立脚本。
"""

import sys
import os
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services import UpdateService
from src.utils.logger import get_logger
from src.utils.time_utils import TimeUtils

logger = get_logger(__name__)


def main():
    """主函数"""
    logger.info("开始每日数据更新任务")
    
    try:
        # 检查是否为交易日
        current_date = TimeUtils.get_current_date()
        if not TimeUtils.is_trading_day(current_date):
            logger.info(f"今日 {current_date} 不是交易日，跳过更新")
            return
        
        # 检查是否在交易时间后
        current_time = TimeUtils.get_current_time()
        if current_time.hour < 15:
            logger.warning(f"当前时间 {current_time.strftime('%H:%M')} 可能早于收盘时间，建议15:00后执行")
        
        # 创建更新服务
        update_service = UpdateService()
        
        # 执行增量更新
        logger.info("开始增量更新...")
        result = update_service.incremental_update(
            periods=["1d", "1h", "30m", "5m"]  # 更新常用周期
        )
        
        # 输出结果
        if result["status"] == "success":
            logger.info(f"每日更新成功完成: 成功 {result['total_success']}, 失败 {result['total_failed']}")
        else:
            logger.warning(f"每日更新部分失败: 成功 {result['total_success']}, 失败 {result['total_failed']}")
        
        # 如果是周末，额外更新周线数据
        if current_date.weekday() == 4:  # 周五
            logger.info("周五额外更新周线数据...")
            weekly_result = update_service.update_all_stocks(
                periods=["1w"],
                start_date=current_date - timedelta(days=7),
                end_date=current_date
            )
            logger.info(f"周线数据更新完成: {weekly_result}")
        
        # 如果是月末，更新月线数据
        tomorrow = current_date + timedelta(days=1)
        if tomorrow.month != current_date.month:  # 今天是月末
            logger.info("月末额外更新月线数据...")
            monthly_result = update_service.update_all_stocks(
                periods=["1M"],
                start_date=current_date.replace(day=1),
                end_date=current_date
            )
            logger.info(f"月线数据更新完成: {monthly_result}")
        
        logger.info("每日数据更新任务完成")
        
    except Exception as e:
        logger.error(f"每日数据更新失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
