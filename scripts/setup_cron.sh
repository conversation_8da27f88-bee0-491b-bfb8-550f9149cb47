#!/bin/bash
"""
设置定时任务脚本

自动配置cron定时任务。
"""

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Conda环境路径（根据实际情况修改）
CONDA_ENV_NAME="stock-db"
CONDA_BASE=$(conda info --base)
PYTHON_PATH="$CONDA_BASE/envs/$CONDA_ENV_NAME/bin/python"

# 检查conda环境是否存在
if [ ! -f "$PYTHON_PATH" ]; then
    echo "错误: Conda环境 '$CONDA_ENV_NAME' 不存在"
    echo "请先运行: conda env create -f environment.yml"
    exit 1
fi

# 日志目录
LOG_DIR="${PROJECT_DIR}/logs"
mkdir -p "$LOG_DIR"

# 定时任务配置（激活conda环境后执行）
CRON_JOB="0 18 * * 1-5 cd $PROJECT_DIR && source $CONDA_BASE/etc/profile.d/conda.sh && conda activate $CONDA_ENV_NAME && python scripts/daily_update.py >> $LOG_DIR/daily_update.log 2>&1"

echo "配置定时任务..."
echo "项目目录: $PROJECT_DIR"
echo "Python路径: $PYTHON_PATH"
echo "日志目录: $LOG_DIR"
echo "定时任务: $CRON_JOB"

# 检查是否已存在相同的定时任务
if crontab -l 2>/dev/null | grep -q "daily_update.py"; then
    echo "警告: 已存在相关的定时任务"
    echo "当前的定时任务:"
    crontab -l | grep "daily_update.py"
    echo ""
    read -p "是否要替换现有的定时任务? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 删除现有的定时任务
        crontab -l | grep -v "daily_update.py" | crontab -
        echo "已删除现有的定时任务"
    else
        echo "操作已取消"
        exit 0
    fi
fi

# 添加新的定时任务
(crontab -l 2>/dev/null; echo "$CRON_JOB") | crontab -

if [ $? -eq 0 ]; then
    echo "定时任务设置成功!"
    echo ""
    echo "定时任务详情:"
    echo "  执行时间: 每个工作日 18:00"
    echo "  执行脚本: $SCRIPT_DIR/daily_update.py"
    echo "  日志文件: $LOG_DIR/daily_update.log"
    echo ""
    echo "当前所有定时任务:"
    crontab -l
else
    echo "定时任务设置失败!"
    exit 1
fi

echo ""
echo "其他有用的命令:"
echo "  查看定时任务: crontab -l"
echo "  编辑定时任务: crontab -e"
echo "  删除所有定时任务: crontab -r"
echo "  查看日志: tail -f $LOG_DIR/daily_update.log"
