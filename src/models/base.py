"""
数据模型基类

定义SQLAlchemy基类和通用字段。
"""

from datetime import datetime
from typing import Any
from sqlalchemy import DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import DeclarativeBase, Mapped, mapped_column


class Base(DeclarativeBase):
    """SQLAlchemy基类"""
    
    # 类型注解映射
    type_annotation_map = {
        datetime: DateTime(timezone=True),
    }


class TimestampMixin:
    """时间戳混入类"""
    
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.current_timestamp(),
        comment="创建时间"
    )
    
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.current_timestamp(),
        onupdate=func.current_timestamp(),
        comment="更新时间"
    )


class BaseModel(Base, TimestampMixin):
    """基础模型类"""
    
    __abstract__ = True
    
    def to_dict(self) -> dict[str, Any]:
        """转换为字典"""
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
    
    def update_from_dict(self, data: dict[str, Any]) -> None:
        """从字典更新属性"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """字符串表示"""
        class_name = self.__class__.__name__
        attrs = []
        
        # 显示主键
        for column in self.__table__.primary_key.columns:
            value = getattr(self, column.name, None)
            attrs.append(f"{column.name}={value}")
        
        # 显示其他重要字段
        important_fields = getattr(self, '_repr_fields', [])
        for field in important_fields:
            if hasattr(self, field):
                value = getattr(self, field)
                attrs.append(f"{field}={value}")
        
        return f"{class_name}({', '.join(attrs)})"
