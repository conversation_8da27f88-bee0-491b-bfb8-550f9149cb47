"""
股票数据模型
"""

from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from sqlalchemy import String, Date, DateTime, DECIMAL, BigInteger, Integer, ForeignKey, Index, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel

# 时间周期常量
PERIOD_CHOICES = {
    "1m": "1分钟",
    "5m": "5分钟",
    "30m": "30分钟",
    "1h": "1小时",
    "1d": "日线",
    "1w": "周线",
    "1M": "月线",
    "1y": "年线"
}


class StockData(BaseModel):
    """股票数据表"""
    
    __tablename__ = "stock_data"
    __table_args__ = (
        UniqueConstraint("stock_id", "period", "trade_time", name="uq_stock_data_unique"),
        Index("idx_stock_data_symbol_period", "symbol", "period"),
        Index("idx_stock_data_date", "trade_date"),
        Index("idx_stock_data_time", "trade_time"),
        Index("idx_stock_data_symbol_date", "symbol", "trade_date"),
        {"comment": "股票历史数据表"}
    )
    
    # 显示字段
    _repr_fields = ["symbol", "period", "trade_date", "close_price"]
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="自增主键")
    
    # 关联字段
    stock_id: Mapped[int] = mapped_column(
        ForeignKey("stock_info.id", ondelete="CASCADE"),
        nullable=False,
        comment="股票ID（关联stock_info.id）"
    )
    
    symbol: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        comment="股票代码（冗余字段，提高查询效率）"
    )
    
    # 时间字段
    period: Mapped[str] = mapped_column(
        String(10),
        nullable=False,
        comment="时间周期（1m/5m/30m/1h/1d/1w/1M/1y）"
    )
    
    trade_date: Mapped[date] = mapped_column(
        Date,
        nullable=False,
        comment="交易日期"
    )
    
    trade_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        comment="交易时间（精确到分钟）"
    )
    
    # 价格字段（使用DECIMAL确保精度）
    open_price: Mapped[Decimal] = mapped_column(
        DECIMAL(10, 3),
        nullable=False,
        comment="开盘价"
    )
    
    high_price: Mapped[Decimal] = mapped_column(
        DECIMAL(10, 3),
        nullable=False,
        comment="最高价"
    )
    
    low_price: Mapped[Decimal] = mapped_column(
        DECIMAL(10, 3),
        nullable=False,
        comment="最低价"
    )
    
    close_price: Mapped[Decimal] = mapped_column(
        DECIMAL(10, 3),
        nullable=False,
        comment="收盘价"
    )
    
    # 成交量和成交额
    volume: Mapped[int] = mapped_column(
        BigInteger,
        nullable=False,
        comment="成交量（股）"
    )
    
    amount: Mapped[Decimal] = mapped_column(
        DECIMAL(15, 2),
        nullable=False,
        comment="成交额（元）"
    )
    
    # 技术指标
    turnover_rate: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(8, 4),
        comment="换手率（%）"
    )
    
    price_change: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(10, 3),
        comment="价格变动"
    )
    
    pct_change: Mapped[Optional[Decimal]] = mapped_column(
        DECIMAL(8, 4),
        comment="涨跌幅（%）"
    )
    
    # 关联关系
    stock_info: Mapped["StockInfo"] = relationship(
        "StockInfo",
        back_populates="stock_data"
    )
    
    @property
    def price_range(self) -> Decimal:
        """价格波动范围"""
        return self.high_price - self.low_price
    
    @property
    def price_range_pct(self) -> Optional[Decimal]:
        """价格波动范围百分比"""
        if self.low_price > 0:
            return (self.price_range / self.low_price) * 100
        return None
    
    @property
    def avg_price(self) -> Decimal:
        """平均价格（成交额/成交量）"""
        if self.volume > 0:
            return self.amount / self.volume
        return (self.high_price + self.low_price) / 2
    
    @property
    def is_up(self) -> bool:
        """是否上涨"""
        return self.close_price > self.open_price
    
    @property
    def is_down(self) -> bool:
        """是否下跌"""
        return self.close_price < self.open_price
    
    @property
    def is_flat(self) -> bool:
        """是否平盘"""
        return self.close_price == self.open_price
    
    def calculate_indicators(self, prev_close: Optional[Decimal] = None) -> None:
        """计算技术指标"""
        if prev_close is not None:
            # 计算价格变动
            self.price_change = self.close_price - prev_close
            
            # 计算涨跌幅
            if prev_close > 0:
                self.pct_change = (self.price_change / prev_close) * 100
    
    @classmethod
    def create_from_akshare(cls, stock_id: int, symbol: str, period: str, 
                           data_row: dict, trade_time: datetime) -> "StockData":
        """从AkShare数据创建股票数据"""
        return cls(
            stock_id=stock_id,
            symbol=symbol,
            period=period,
            trade_date=trade_time.date(),
            trade_time=trade_time,
            open_price=Decimal(str(data_row.get("open", 0))),
            high_price=Decimal(str(data_row.get("high", 0))),
            low_price=Decimal(str(data_row.get("low", 0))),
            close_price=Decimal(str(data_row.get("close", 0))),
            volume=int(data_row.get("volume", 0)),
            amount=Decimal(str(data_row.get("amount", 0))),
            turnover_rate=Decimal(str(data_row.get("turnover", 0))) if data_row.get("turnover") else None,
            pct_change=Decimal(str(data_row.get("pct_chg", 0))) if data_row.get("pct_chg") else None,
        )
    
    def validate_data(self) -> list[str]:
        """验证数据有效性"""
        errors = []
        
        # 价格验证
        if self.open_price <= 0:
            errors.append("开盘价必须大于0")
        if self.high_price <= 0:
            errors.append("最高价必须大于0")
        if self.low_price <= 0:
            errors.append("最低价必须大于0")
        if self.close_price <= 0:
            errors.append("收盘价必须大于0")
        
        # 价格逻辑验证
        if self.high_price < self.low_price:
            errors.append("最高价不能小于最低价")
        if self.high_price < max(self.open_price, self.close_price):
            errors.append("最高价不能小于开盘价和收盘价")
        if self.low_price > min(self.open_price, self.close_price):
            errors.append("最低价不能大于开盘价和收盘价")
        
        # 成交量验证
        if self.volume < 0:
            errors.append("成交量不能为负数")
        if self.amount < 0:
            errors.append("成交额不能为负数")
        
        return errors
