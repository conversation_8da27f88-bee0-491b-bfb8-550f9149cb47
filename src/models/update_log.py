"""
数据更新日志模型
"""

from datetime import datetime
from typing import Optional
from sqlalchemy import String, Date, Integer, Text, Index
from sqlalchemy.orm import Mapped, mapped_column

from .base import BaseModel


class UpdateLog(BaseModel):
    """数据更新日志表"""
    
    __tablename__ = "update_log"
    __table_args__ = (
        Index("idx_update_log_type", "task_type"),
        Index("idx_update_log_symbol", "symbol"),
        Index("idx_update_log_status", "status"),
        Index("idx_update_log_time", "start_time"),
        {"comment": "数据更新日志表"}
    )
    
    # 显示字段
    _repr_fields = ["task_type", "symbol", "status", "start_time"]
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="自增主键")
    
    # 任务信息
    task_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="任务类型（stock_info/stock_data）"
    )
    
    symbol: Mapped[Optional[str]] = mapped_column(
        String(20),
        comment="股票代码（如果是股票数据更新）"
    )
    
    period: Mapped[Optional[str]] = mapped_column(
        String(10),
        comment="时间周期（如果是股票数据更新）"
    )
    
    # 时间范围
    start_date: Mapped[Optional[datetime]] = mapped_column(
        Date,
        comment="更新开始日期"
    )
    
    end_date: Mapped[Optional[datetime]] = mapped_column(
        Date,
        comment="更新结束日期"
    )
    
    # 统计信息
    total_records: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="总记录数"
    )
    
    success_records: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="成功记录数"
    )
    
    failed_records: Mapped[int] = mapped_column(
        Integer,
        default=0,
        comment="失败记录数"
    )
    
    # 状态信息
    status: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        comment="状态（running/success/failed/partial）"
    )
    
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="错误信息"
    )
    
    # 时间信息
    start_time: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        comment="开始时间"
    )
    
    end_time: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        comment="结束时间"
    )
    
    duration_seconds: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="执行时长（秒）"
    )
    
    @property
    def success_rate(self) -> float:
        """成功率"""
        if self.total_records == 0:
            return 0.0
        return (self.success_records / self.total_records) * 100
    
    @property
    def is_running(self) -> bool:
        """是否正在运行"""
        return self.status == "running"
    
    @property
    def is_success(self) -> bool:
        """是否成功"""
        return self.status == "success"
    
    @property
    def is_failed(self) -> bool:
        """是否失败"""
        return self.status == "failed"
    
    @property
    def is_partial(self) -> bool:
        """是否部分成功"""
        return self.status == "partial"
    
    def finish_success(self) -> None:
        """标记为成功完成"""
        self.status = "success"
        self.end_time = datetime.now()
        if self.start_time:
            self.duration_seconds = int((self.end_time - self.start_time).total_seconds())
    
    def finish_failed(self, error_message: str) -> None:
        """标记为失败"""
        self.status = "failed"
        self.error_message = error_message
        self.end_time = datetime.now()
        if self.start_time:
            self.duration_seconds = int((self.end_time - self.start_time).total_seconds())
    
    def finish_partial(self, error_message: str = None) -> None:
        """标记为部分成功"""
        self.status = "partial"
        if error_message:
            self.error_message = error_message
        self.end_time = datetime.now()
        if self.start_time:
            self.duration_seconds = int((self.end_time - self.start_time).total_seconds())
    
    def update_progress(self, success_count: int, failed_count: int = 0) -> None:
        """更新进度"""
        self.success_records = success_count
        self.failed_records = failed_count
        self.total_records = success_count + failed_count
    
    @classmethod
    def create_task(cls, task_type: str, symbol: str = None, period: str = None,
                   start_date: datetime = None, end_date: datetime = None) -> "UpdateLog":
        """创建新任务"""
        return cls(
            task_type=task_type,
            symbol=symbol,
            period=period,
            start_date=start_date,
            end_date=end_date,
            status="running",
            start_time=datetime.now(),
            total_records=0,
            success_records=0,
            failed_records=0
        )
