"""
股票基本信息模型
"""

from datetime import date
from typing import Optional, List
from sqlalchemy import String, Date, Boolean, Index
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import BaseModel


class StockInfo(BaseModel):
    """股票基本信息表"""
    
    __tablename__ = "stock_info"
    __table_args__ = (
        Index("idx_stock_symbol", "symbol", unique=True),
        Index("idx_stock_market", "market"),
        Index("idx_stock_active", "is_active"),
        {"comment": "股票基本信息表"}
    )
    
    # 显示字段
    _repr_fields = ["symbol", "name", "market"]
    
    # 主键
    id: Mapped[int] = mapped_column(primary_key=True, comment="自增主键")
    
    # 基本信息
    symbol: Mapped[str] = mapped_column(
        String(20), 
        unique=True, 
        nullable=False,
        comment="股票代码（如：000001.SZ）"
    )
    
    name: Mapped[str] = mapped_column(
        String(100), 
        nullable=False,
        comment="股票名称"
    )
    
    market: Mapped[str] = mapped_column(
        String(10), 
        nullable=False,
        comment="市场类型（SZ/SH/BJ）"
    )
    
    # 分类信息
    industry: Mapped[Optional[str]] = mapped_column(
        String(50),
        comment="所属行业"
    )
    
    sector: Mapped[Optional[str]] = mapped_column(
        String(50),
        comment="所属板块"
    )
    
    # 日期信息
    list_date: Mapped[Optional[date]] = mapped_column(
        Date,
        comment="上市日期"
    )
    
    delist_date: Mapped[Optional[date]] = mapped_column(
        Date,
        comment="退市日期（如果已退市）"
    )
    
    # 状态信息
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        default=True,
        comment="是否活跃交易"
    )
    
    # 关联关系
    stock_data: Mapped[List["StockData"]] = relationship(
        "StockData",
        back_populates="stock_info",
        cascade="all, delete-orphan"
    )
    
    @property
    def is_listed(self) -> bool:
        """是否已上市"""
        return self.list_date is not None
    
    @property
    def is_delisted(self) -> bool:
        """是否已退市"""
        return self.delist_date is not None
    
    @property
    def market_display(self) -> str:
        """市场显示名称"""
        market_names = {
            "SZ": "深圳证券交易所",
            "SH": "上海证券交易所", 
            "BJ": "北京证券交易所"
        }
        return market_names.get(self.market, self.market)
    
    def get_symbol_without_suffix(self) -> str:
        """获取不带后缀的股票代码"""
        return self.symbol.split(".")[0] if "." in self.symbol else self.symbol
    
    def get_market_from_symbol(self) -> str:
        """从股票代码获取市场"""
        if "." in self.symbol:
            suffix = self.symbol.split(".")[1]
            return suffix
        return ""
    
    @classmethod
    def create_from_akshare(cls, symbol: str, name: str, **kwargs) -> "StockInfo":
        """从AkShare数据创建股票信息"""
        # 解析市场信息
        market = ""
        if "." in symbol:
            market = symbol.split(".")[1]
        elif symbol.startswith("0") or symbol.startswith("3"):
            market = "SZ"
            symbol = f"{symbol}.SZ"
        elif symbol.startswith("6"):
            market = "SH"
            symbol = f"{symbol}.SH"
        elif symbol.startswith("8") or symbol.startswith("4"):
            market = "BJ"
            symbol = f"{symbol}.BJ"
        
        return cls(
            symbol=symbol,
            name=name,
            market=market,
            **kwargs
        )
