"""
命令行接口

提供股票数据管理的命令行工具。
"""

import sys
import argparse
from datetime import date, datetime
from typing import List, Optional

from .database import init_database, check_database_connection, get_database_info
from .services import StockDataService, UpdateService
from .models import PERIOD_CHOICES
from .utils.logger import get_logger
from .utils.time_utils import TimeUtils

logger = get_logger(__name__)


def init_db_command(args):
    """初始化数据库"""
    print("正在初始化数据库...")
    
    if args.drop_all:
        confirm = input("确定要删除所有数据吗？(yes/no): ")
        if confirm.lower() != 'yes':
            print("操作已取消")
            return
    
    try:
        init_database(drop_all=args.drop_all)
        print("数据库初始化成功！")
    except Exception as e:
        print(f"数据库初始化失败: {str(e)}")
        sys.exit(1)


def check_db_command(args):
    """检查数据库连接"""
    print("正在检查数据库连接...")
    
    if check_database_connection():
        print("数据库连接正常！")
        
        # 显示数据库信息
        db_info = get_database_info()
        if db_info:
            print("\n数据库信息:")
            for key, value in db_info.items():
                print(f"  {key}: {value}")
    else:
        print("数据库连接失败！")
        sys.exit(1)


def update_stocks_command(args):
    """更新股票列表"""
    print("正在更新股票列表...")
    
    try:
        service = StockDataService()
        count = service.update_stock_list()
        print(f"股票列表更新完成，新增/更新: {count} 只股票")
    except Exception as e:
        print(f"更新股票列表失败: {str(e)}")
        sys.exit(1)


def update_data_command(args):
    """更新股票数据"""
    print("正在更新股票数据...")
    
    # 解析参数
    periods = args.periods if args.periods else ["1d"]
    start_date = None
    end_date = None
    
    if args.start_date:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d").date()
    if args.end_date:
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d").date()
    
    try:
        service = UpdateService()
        
        if args.symbol:
            # 更新单只股票
            result = service.update_single_stock(
                symbol=args.symbol,
                periods=periods,
                start_date=start_date,
                end_date=end_date
            )
            print(f"股票 {args.symbol} 更新完成:")
            print(f"  成功: {result['total_success']}")
            print(f"  失败: {result['total_failed']}")
            print(f"  状态: {result['status']}")
        else:
            # 批量更新
            if args.incremental:
                result = service.incremental_update(periods=periods)
                print("增量更新完成:")
            else:
                result = service.update_all_stocks(
                    periods=periods,
                    start_date=start_date,
                    end_date=end_date,
                    force_update=args.force
                )
                print("批量更新完成:")
            
            print(f"  总任务: {result.get('total_tasks', 0)}")
            print(f"  成功: {result['total_success'] if 'total_success' in result else result['success_count']}")
            print(f"  失败: {result['total_failed'] if 'total_failed' in result else result['failed_count']}")
            print(f"  状态: {result['status']}")
    
    except Exception as e:
        print(f"更新股票数据失败: {str(e)}")
        sys.exit(1)


def query_data_command(args):
    """查询股票数据"""
    print(f"正在查询股票数据: {args.symbol}")
    
    # 解析参数
    start_date = None
    end_date = None
    
    if args.start_date:
        start_date = datetime.strptime(args.start_date, "%Y-%m-%d").date()
    if args.end_date:
        end_date = datetime.strptime(args.end_date, "%Y-%m-%d").date()
    
    try:
        service = StockDataService()
        data_list = service.get_stock_data(
            symbol=args.symbol,
            period=args.period,
            start_date=start_date,
            end_date=end_date,
            limit=args.limit
        )
        
        if not data_list:
            print("未找到数据")
            return
        
        print(f"找到 {len(data_list)} 条数据:")
        print("日期\t\t开盘\t最高\t最低\t收盘\t成交量")
        print("-" * 60)
        
        for data in data_list[:10]:  # 只显示前10条
            print(f"{data.trade_date}\t{data.open_price}\t{data.high_price}\t"
                  f"{data.low_price}\t{data.close_price}\t{data.volume}")
        
        if len(data_list) > 10:
            print(f"... 还有 {len(data_list) - 10} 条数据")
    
    except Exception as e:
        print(f"查询股票数据失败: {str(e)}")
        sys.exit(1)


def stats_command(args):
    """显示统计信息"""
    print("正在获取统计信息...")
    
    try:
        service = StockDataService()
        stats = service.get_data_statistics(
            symbol=args.symbol,
            period=args.period
        )
        
        print("数据统计:")
        print(f"  总记录数: {stats['total_count']:,}")
        print(f"  股票数量: {stats['symbols_count']:,}")
        print(f"  最早日期: {stats['earliest_date']}")
        print(f"  最新日期: {stats['latest_date']}")
        print(f"  时间周期: {', '.join(stats['periods'])}")
        
        # 显示周期说明
        if stats['periods']:
            print("\n周期说明:")
            for period in stats['periods']:
                if period in PERIOD_CHOICES:
                    print(f"  {period}: {PERIOD_CHOICES[period]}")
    
    except Exception as e:
        print(f"获取统计信息失败: {str(e)}")
        sys.exit(1)


def schedule_command(args):
    """定时任务"""
    print("启动定时更新任务...")
    
    import schedule
    import time
    
    def daily_update():
        """每日更新任务"""
        print(f"[{datetime.now()}] 开始每日数据更新...")
        try:
            service = UpdateService()
            result = service.incremental_update()
            print(f"每日更新完成: {result}")
        except Exception as e:
            print(f"每日更新失败: {str(e)}")
    
    # 设置定时任务
    schedule_time = args.time or "18:00"
    schedule.every().day.at(schedule_time).do(daily_update)
    
    print(f"定时任务已设置，每日 {schedule_time} 执行更新")
    print("按 Ctrl+C 停止...")
    
    try:
        while True:
            schedule.run_pending()
            time.sleep(60)  # 每分钟检查一次
    except KeyboardInterrupt:
        print("\n定时任务已停止")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="股票数据管理工具")
    subparsers = parser.add_subparsers(dest="command", help="可用命令")
    
    # 初始化数据库
    init_parser = subparsers.add_parser("init-db", help="初始化数据库")
    init_parser.add_argument("--drop-all", action="store_true", help="删除所有现有数据")
    init_parser.set_defaults(func=init_db_command)
    
    # 检查数据库
    check_parser = subparsers.add_parser("check-db", help="检查数据库连接")
    check_parser.set_defaults(func=check_db_command)
    
    # 更新股票列表
    stocks_parser = subparsers.add_parser("update-stocks", help="更新股票列表")
    stocks_parser.set_defaults(func=update_stocks_command)
    
    # 更新数据
    update_parser = subparsers.add_parser("update-data", help="更新股票数据")
    update_parser.add_argument("--symbol", help="股票代码（如：000001.SZ）")
    update_parser.add_argument("--periods", nargs="+", choices=list(PERIOD_CHOICES.keys()),
                              help="时间周期")
    update_parser.add_argument("--start-date", help="开始日期 (YYYY-MM-DD)")
    update_parser.add_argument("--end-date", help="结束日期 (YYYY-MM-DD)")
    update_parser.add_argument("--incremental", action="store_true", help="增量更新")
    update_parser.add_argument("--force", action="store_true", help="强制更新")
    update_parser.set_defaults(func=update_data_command)
    
    # 查询数据
    query_parser = subparsers.add_parser("query", help="查询股票数据")
    query_parser.add_argument("symbol", help="股票代码（如：000001.SZ）")
    query_parser.add_argument("--period", default="1d", choices=list(PERIOD_CHOICES.keys()),
                             help="时间周期")
    query_parser.add_argument("--start-date", help="开始日期 (YYYY-MM-DD)")
    query_parser.add_argument("--end-date", help="结束日期 (YYYY-MM-DD)")
    query_parser.add_argument("--limit", type=int, default=20, help="限制返回数量")
    query_parser.set_defaults(func=query_data_command)
    
    # 统计信息
    stats_parser = subparsers.add_parser("stats", help="显示统计信息")
    stats_parser.add_argument("--symbol", help="股票代码")
    stats_parser.add_argument("--period", help="时间周期")
    stats_parser.set_defaults(func=stats_command)
    
    # 定时任务
    schedule_parser = subparsers.add_parser("schedule", help="启动定时更新")
    schedule_parser.add_argument("--time", default="18:00", help="更新时间 (HH:MM)")
    schedule_parser.set_defaults(func=schedule_command)
    
    # 解析参数
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    # 执行命令
    args.func(args)


if __name__ == "__main__":
    main()
