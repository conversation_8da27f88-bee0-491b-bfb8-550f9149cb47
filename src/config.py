"""
配置管理模块

使用Pydantic Settings进行配置管理，支持环境变量和.env文件。
"""

from typing import Optional
from pydantic import Field, validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    
    host: str = Field(default="localhost", description="数据库主机")
    port: int = Field(default=5432, description="数据库端口")
    name: str = Field(default="stock_db", description="数据库名称")
    user: str = Field(default="postgres", description="数据库用户名")
    password: str = Field(description="数据库密码")
    pool_size: int = Field(default=10, description="连接池大小")
    max_overflow: int = Field(default=20, description="连接池最大溢出")
    
    model_config = SettingsConfigDict(env_prefix="DB_")
    
    @property
    def url(self) -> str:
        """构建数据库连接URL"""
        return f"postgresql+psycopg://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"
    
    @property
    def async_url(self) -> str:
        """构建异步数据库连接URL"""
        return f"postgresql+asyncpg://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"


class LogSettings(BaseSettings):
    """日志配置"""
    
    level: str = Field(default="INFO", description="日志级别")
    file_path: str = Field(default="logs/stock_db.log", description="日志文件路径")
    rotation: str = Field(default="1 day", description="日志轮转周期")
    retention: str = Field(default="30 days", description="日志保留时间")
    format: str = Field(
        default="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
        description="日志格式"
    )
    
    model_config = SettingsConfigDict(env_prefix="LOG_")
    
    @validator("level")
    def validate_level(cls, v):
        valid_levels = ["TRACE", "DEBUG", "INFO", "SUCCESS", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是: {', '.join(valid_levels)}")
        return v.upper()


class AkShareSettings(BaseSettings):
    """AkShare配置"""
    
    timeout: int = Field(default=30, description="请求超时时间（秒）")
    retry_times: int = Field(default=3, description="重试次数")
    retry_delay: int = Field(default=1, description="重试延迟（秒）")
    
    model_config = SettingsConfigDict(env_prefix="AKSHARE_")


class UpdateSettings(BaseSettings):
    """数据更新配置"""
    
    schedule_time: str = Field(default="18:00", description="定时更新时间")
    batch_size: int = Field(default=100, description="批量处理大小")
    concurrent_limit: int = Field(default=5, description="并发限制")
    
    model_config = SettingsConfigDict(env_prefix="UPDATE_")
    
    @validator("schedule_time")
    def validate_schedule_time(cls, v):
        try:
            hour, minute = map(int, v.split(":"))
            if not (0 <= hour <= 23 and 0 <= minute <= 59):
                raise ValueError
        except (ValueError, AttributeError):
            raise ValueError("时间格式必须是 HH:MM，例如 18:00")
        return v


class RedisSettings(BaseSettings):
    """Redis配置（可选，用于Celery）"""
    
    host: str = Field(default="localhost", description="Redis主机")
    port: int = Field(default=6379, description="Redis端口")
    db: int = Field(default=0, description="Redis数据库")
    password: Optional[str] = Field(default=None, description="Redis密码")
    
    model_config = SettingsConfigDict(env_prefix="REDIS_")
    
    @property
    def url(self) -> str:
        """构建Redis连接URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"


class AppSettings(BaseSettings):
    """应用配置"""
    
    name: str = Field(default="stock-db", description="应用名称")
    version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    timezone: str = Field(default="Asia/Shanghai", description="时区")
    
    model_config = SettingsConfigDict(env_prefix="APP_")


class Settings(BaseSettings):
    """主配置类"""
    
    # 子配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    log: LogSettings = Field(default_factory=LogSettings)
    akshare: AkShareSettings = Field(default_factory=AkShareSettings)
    update: UpdateSettings = Field(default_factory=UpdateSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    app: AppSettings = Field(default_factory=AppSettings)
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 初始化子配置
        self.database = DatabaseSettings()
        self.log = LogSettings()
        self.akshare = AkShareSettings()
        self.update = UpdateSettings()
        self.redis = RedisSettings()
        self.app = AppSettings()


# 全局配置实例
settings = Settings()


def get_settings() -> Settings:
    """获取配置实例"""
    return settings
