"""
数据更新服务

提供定时更新和手动更新功能。
"""

from datetime import date, datetime, timedelta
from typing import List, Optional, Dict, Any
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from ..database import get_db_context
from ..models import StockInfo, UpdateLog, PERIOD_CHOICES
from ..services.stock_data_service import StockDataService
from ..config import settings
from ..utils.logger import get_logger, LogTimer
from ..utils.time_utils import TimeUtils

logger = get_logger(__name__)


class UpdateService:
    """数据更新服务"""
    
    def __init__(self):
        self.stock_data_service = StockDataService()
        self.max_workers = settings.update.concurrent_limit
        self.batch_size = settings.update.batch_size
        self._update_lock = threading.Lock()
    
    def update_all_stocks(self, periods: List[str] = None, 
                         start_date: Optional[date] = None,
                         end_date: Optional[date] = None,
                         force_update: bool = False) -> Dict[str, Any]:
        """更新所有股票数据"""
        
        if periods is None:
            periods = list(PERIOD_CHOICES.keys())
        
        # 创建更新日志
        update_log = UpdateLog.create_task(
            task_type="stock_data_batch",
            start_date=start_date,
            end_date=end_date
        )
        
        with get_db_context() as session:
            session.add(update_log)
            session.flush()
            log_id = update_log.id
        
        try:
            with LogTimer("批量更新股票数据", logger.name):
                # 获取股票列表
                stock_list = self._get_active_stocks()
                if not stock_list:
                    raise Exception("未找到活跃股票")
                
                total_tasks = len(stock_list) * len(periods)
                completed_tasks = 0
                success_count = 0
                failed_count = 0
                
                logger.info(f"开始批量更新: {len(stock_list)} 只股票, {len(periods)} 个周期, 总任务数: {total_tasks}")
                
                # 按周期分组更新
                for period in periods:
                    logger.info(f"更新周期: {period} ({PERIOD_CHOICES[period]})")
                    
                    # 确定时间范围
                    if not start_date or not end_date:
                        period_start, period_end = TimeUtils.get_period_range(period)
                        if not start_date:
                            start_date = period_start
                        if not end_date:
                            end_date = period_end
                    
                    # 并发更新
                    period_success, period_failed = self._update_stocks_concurrent(
                        stock_list, period, start_date, end_date, force_update
                    )
                    
                    success_count += period_success
                    failed_count += period_failed
                    completed_tasks += len(stock_list)
                    
                    logger.info(f"周期 {period} 更新完成: 成功 {period_success}, 失败 {period_failed}")
                
                # 更新日志状态
                with get_db_context() as session:
                    log = session.query(UpdateLog).get(log_id)
                    if log:
                        log.update_progress(success_count, failed_count)
                        if failed_count == 0:
                            log.finish_success()
                        else:
                            log.finish_partial(f"部分更新失败: {failed_count}/{total_tasks}")
                
                result = {
                    "status": "success" if failed_count == 0 else "partial",
                    "total_tasks": total_tasks,
                    "success_count": success_count,
                    "failed_count": failed_count,
                    "periods": periods,
                    "stocks_count": len(stock_list)
                }
                
                logger.info(f"批量更新完成: {result}")
                return result
        
        except Exception as e:
            # 更新日志状态为失败
            with get_db_context() as session:
                log = session.query(UpdateLog).get(log_id)
                if log:
                    log.finish_failed(str(e))
            
            logger.error(f"批量更新失败: {str(e)}")
            raise
    
    def update_single_stock(self, symbol: str, periods: List[str] = None,
                           start_date: Optional[date] = None,
                           end_date: Optional[date] = None) -> Dict[str, Any]:
        """更新单只股票数据"""
        
        if periods is None:
            periods = ["1d"]  # 默认只更新日线
        
        # 创建更新日志
        update_log = UpdateLog.create_task(
            task_type="stock_data_single",
            symbol=symbol,
            start_date=start_date,
            end_date=end_date
        )
        
        with get_db_context() as session:
            session.add(update_log)
            session.flush()
            log_id = update_log.id
        
        try:
            with LogTimer(f"更新股票数据: {symbol}", logger.name):
                total_success = 0
                total_failed = 0
                period_results = {}
                
                for period in periods:
                    logger.info(f"更新 {symbol} {period} 数据")
                    
                    # 确定时间范围
                    update_start = start_date
                    update_end = end_date
                    
                    if not update_start or not update_end:
                        period_start, period_end = TimeUtils.get_period_range(period)
                        if not update_start:
                            update_start = period_start
                        if not update_end:
                            update_end = period_end
                    
                    # 更新数据
                    success, failed = self.stock_data_service.update_stock_data(
                        symbol, period, update_start, update_end
                    )
                    
                    total_success += success
                    total_failed += failed
                    period_results[period] = {"success": success, "failed": failed}
                    
                    logger.info(f"{symbol} {period} 更新完成: 成功 {success}, 失败 {failed}")
                
                # 更新日志状态
                with get_db_context() as session:
                    log = session.query(UpdateLog).get(log_id)
                    if log:
                        log.update_progress(total_success, total_failed)
                        if total_failed == 0:
                            log.finish_success()
                        else:
                            log.finish_partial(f"部分更新失败: {total_failed}")
                
                result = {
                    "status": "success" if total_failed == 0 else "partial",
                    "symbol": symbol,
                    "total_success": total_success,
                    "total_failed": total_failed,
                    "period_results": period_results
                }
                
                logger.info(f"股票 {symbol} 更新完成: {result}")
                return result
        
        except Exception as e:
            # 更新日志状态为失败
            with get_db_context() as session:
                log = session.query(UpdateLog).get(log_id)
                if log:
                    log.finish_failed(str(e))
            
            logger.error(f"股票 {symbol} 更新失败: {str(e)}")
            raise
    
    def incremental_update(self, periods: List[str] = None) -> Dict[str, Any]:
        """增量更新（只更新最新数据）"""
        
        if periods is None:
            periods = ["1d", "1h", "30m", "5m"]  # 常用周期
        
        logger.info("开始增量更新")
        
        # 获取活跃股票
        stock_list = self._get_active_stocks()
        if not stock_list:
            logger.warning("未找到活跃股票")
            return {"status": "failed", "message": "未找到活跃股票"}
        
        total_success = 0
        total_failed = 0
        
        for period in periods:
            logger.info(f"增量更新周期: {period}")
            
            # 确定更新时间范围（最近几天）
            end_date = TimeUtils.get_current_date()
            if period in ["1m", "5m", "30m", "1h"]:
                start_date = end_date - timedelta(days=3)  # 分钟级数据更新最近3天
            else:
                start_date = end_date - timedelta(days=7)  # 日线级数据更新最近7天
            
            # 并发更新
            period_success, period_failed = self._update_stocks_concurrent(
                stock_list, period, start_date, end_date, force_update=True
            )
            
            total_success += period_success
            total_failed += period_failed
            
            logger.info(f"周期 {period} 增量更新完成: 成功 {period_success}, 失败 {period_failed}")
        
        result = {
            "status": "success" if total_failed == 0 else "partial",
            "total_success": total_success,
            "total_failed": total_failed,
            "periods": periods,
            "stocks_count": len(stock_list)
        }
        
        logger.info(f"增量更新完成: {result}")
        return result
    
    def _get_active_stocks(self) -> List[str]:
        """获取活跃股票列表"""
        with get_db_context() as session:
            stocks = session.query(StockInfo.symbol).filter(
                StockInfo.is_active == True
            ).all()
            
            return [stock.symbol for stock in stocks]
    
    def _update_stocks_concurrent(self, stock_list: List[str], period: str,
                                 start_date: date, end_date: date,
                                 force_update: bool = False) -> tuple[int, int]:
        """并发更新股票数据"""
        
        success_count = 0
        failed_count = 0
        
        # 分批处理
        batches = [stock_list[i:i + self.batch_size] 
                  for i in range(0, len(stock_list), self.batch_size)]
        
        for batch_idx, batch in enumerate(batches):
            logger.info(f"处理批次 {batch_idx + 1}/{len(batches)}, 股票数: {len(batch)}")
            
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交任务
                future_to_symbol = {
                    executor.submit(
                        self._update_single_stock_safe,
                        symbol, period, start_date, end_date, force_update
                    ): symbol
                    for symbol in batch
                }
                
                # 收集结果
                for future in as_completed(future_to_symbol):
                    symbol = future_to_symbol[future]
                    try:
                        success, failed = future.result()
                        success_count += success
                        failed_count += failed
                    except Exception as e:
                        logger.error(f"更新股票 {symbol} 失败: {str(e)}")
                        failed_count += 1
        
        return success_count, failed_count
    
    def _update_single_stock_safe(self, symbol: str, period: str,
                                 start_date: date, end_date: date,
                                 force_update: bool = False) -> tuple[int, int]:
        """安全的单股票更新（带异常处理）"""
        try:
            # 检查是否需要更新
            if not force_update:
                latest_date = self.stock_data_service.get_latest_data_date(symbol, period)
                if latest_date and latest_date >= end_date:
                    logger.debug(f"股票 {symbol} {period} 数据已是最新，跳过更新")
                    return 0, 0
            
            return self.stock_data_service.update_stock_data(
                symbol, period, start_date, end_date
            )
        
        except Exception as e:
            logger.error(f"更新股票 {symbol} {period} 失败: {str(e)}")
            return 0, 1
    
    def get_update_status(self) -> Dict[str, Any]:
        """获取更新状态"""
        with get_db_context() as session:
            # 最近的更新记录
            recent_logs = session.query(UpdateLog).order_by(
                UpdateLog.start_time.desc()
            ).limit(10).all()
            
            # 正在运行的任务
            running_tasks = session.query(UpdateLog).filter(
                UpdateLog.status == "running"
            ).all()
            
            return {
                "running_tasks": len(running_tasks),
                "recent_updates": [
                    {
                        "id": log.id,
                        "task_type": log.task_type,
                        "symbol": log.symbol,
                        "period": log.period,
                        "status": log.status,
                        "success_rate": log.success_rate,
                        "start_time": log.start_time,
                        "duration_seconds": log.duration_seconds
                    }
                    for log in recent_logs
                ]
            }
