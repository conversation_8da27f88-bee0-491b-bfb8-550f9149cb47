"""
股票数据服务

提供股票数据的CRUD操作和业务逻辑。
"""

from datetime import date, datetime, timedelta
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc, func

from ..database import get_db_context
from ..models import StockInfo, StockData, UpdateLog
from ..services.akshare_service import AkShareService
from ..utils.logger import get_logger, LogTimer
from ..utils.time_utils import TimeUtils

logger = get_logger(__name__)


class StockDataService:
    """股票数据服务"""
    
    def __init__(self):
        self.akshare_service = AkShareService()
    
    def get_or_create_stock_info(self, symbol: str, name: str = None) -> Optional[StockInfo]:
        """获取或创建股票信息"""
        with get_db_context() as session:
            # 先查找是否存在
            stock_info = session.query(StockInfo).filter(StockInfo.symbol == symbol).first()
            
            if stock_info:
                return stock_info
            
            # 如果不存在，从AkShare获取信息
            if not name:
                akshare_info = self.akshare_service.get_stock_info(symbol)
                if akshare_info:
                    name = akshare_info.get('name', '')
                else:
                    logger.warning(f"无法获取股票信息: {symbol}")
                    return None
            
            # 创建新的股票信息
            stock_info = StockInfo.create_from_akshare(symbol, name)
            session.add(stock_info)
            session.flush()  # 获取ID
            
            logger.info(f"创建股票信息: {symbol} - {name}")
            return stock_info
    
    def update_stock_list(self) -> int:
        """更新股票列表"""
        with LogTimer("更新股票列表", logger.name):
            stock_list_df = self.akshare_service.get_stock_list()
            
            if stock_list_df is None or stock_list_df.empty:
                logger.error("获取股票列表失败")
                return 0
            
            updated_count = 0
            with get_db_context() as session:
                for _, row in stock_list_df.iterrows():
                    symbol = row.get('symbol', '')
                    name = row.get('name', '')
                    
                    if not symbol or not name:
                        continue
                    
                    # 检查是否已存在
                    existing = session.query(StockInfo).filter(StockInfo.symbol == symbol).first()
                    
                    if not existing:
                        stock_info = StockInfo.create_from_akshare(symbol, name)
                        session.add(stock_info)
                        updated_count += 1
                    else:
                        # 更新名称（如果有变化）
                        if existing.name != name:
                            existing.name = name
                            updated_count += 1
            
            logger.info(f"股票列表更新完成，新增/更新: {updated_count}")
            return updated_count
    
    def get_stock_data(self, symbol: str, period: str, 
                      start_date: Optional[date] = None,
                      end_date: Optional[date] = None,
                      limit: Optional[int] = None) -> List[StockData]:
        """获取股票数据"""
        with get_db_context() as session:
            query = session.query(StockData).filter(
                and_(
                    StockData.symbol == symbol,
                    StockData.period == period
                )
            )
            
            if start_date:
                query = query.filter(StockData.trade_date >= start_date)
            if end_date:
                query = query.filter(StockData.trade_date <= end_date)
            
            query = query.order_by(desc(StockData.trade_time))
            
            if limit:
                query = query.limit(limit)
            
            return query.all()
    
    def save_stock_data(self, stock_data_list: List[StockData]) -> Tuple[int, int]:
        """批量保存股票数据"""
        if not stock_data_list:
            return 0, 0
        
        success_count = 0
        failed_count = 0
        
        with get_db_context() as session:
            for stock_data in stock_data_list:
                try:
                    # 检查数据有效性
                    errors = stock_data.validate_data()
                    if errors:
                        logger.warning(f"数据验证失败: {stock_data.symbol} {stock_data.trade_time}, 错误: {errors}")
                        failed_count += 1
                        continue
                    
                    # 检查是否已存在（去重）
                    existing = session.query(StockData).filter(
                        and_(
                            StockData.stock_id == stock_data.stock_id,
                            StockData.period == stock_data.period,
                            StockData.trade_time == stock_data.trade_time
                        )
                    ).first()
                    
                    if existing:
                        # 更新现有数据
                        existing.open_price = stock_data.open_price
                        existing.high_price = stock_data.high_price
                        existing.low_price = stock_data.low_price
                        existing.close_price = stock_data.close_price
                        existing.volume = stock_data.volume
                        existing.amount = stock_data.amount
                        existing.turnover_rate = stock_data.turnover_rate
                        existing.pct_change = stock_data.pct_change
                    else:
                        # 添加新数据
                        session.add(stock_data)
                    
                    success_count += 1
                    
                except Exception as e:
                    logger.error(f"保存股票数据失败: {stock_data.symbol} {stock_data.trade_time}, 错误: {str(e)}")
                    failed_count += 1
        
        logger.info(f"股票数据保存完成，成功: {success_count}, 失败: {failed_count}")
        return success_count, failed_count
    
    def update_stock_data(self, symbol: str, period: str,
                         start_date: Optional[date] = None,
                         end_date: Optional[date] = None) -> Tuple[int, int]:
        """更新股票数据"""
        
        # 获取或创建股票信息
        stock_info = self.get_or_create_stock_info(symbol)
        if not stock_info:
            logger.error(f"无法获取股票信息: {symbol}")
            return 0, 1
        
        # 从AkShare获取数据
        with LogTimer(f"获取股票数据: {symbol} {period}", logger.name):
            df = self.akshare_service.get_stock_data(symbol, period, start_date, end_date)
        
        if df is None or df.empty:
            logger.warning(f"未获取到股票数据: {symbol} {period}")
            return 0, 1
        
        # 转换为模型对象
        stock_data_list = []
        for _, row in df.iterrows():
            try:
                trade_time = row['trade_time']
                stock_data = StockData.create_from_akshare(
                    stock_id=stock_info.id,
                    symbol=symbol,
                    period=period,
                    data_row=row.to_dict(),
                    trade_time=trade_time
                )
                stock_data_list.append(stock_data)
            except Exception as e:
                logger.error(f"转换股票数据失败: {symbol} {row.get('trade_time', '')}, 错误: {str(e)}")
        
        # 保存数据
        return self.save_stock_data(stock_data_list)
    
    def get_latest_data_date(self, symbol: str, period: str) -> Optional[date]:
        """获取最新数据日期"""
        with get_db_context() as session:
            result = session.query(func.max(StockData.trade_date)).filter(
                and_(
                    StockData.symbol == symbol,
                    StockData.period == period
                )
            ).scalar()
            
            return result
    
    def get_data_statistics(self, symbol: str = None, period: str = None) -> Dict[str, Any]:
        """获取数据统计信息"""
        with get_db_context() as session:
            query = session.query(StockData)
            
            if symbol:
                query = query.filter(StockData.symbol == symbol)
            if period:
                query = query.filter(StockData.period == period)
            
            total_count = query.count()
            
            if total_count == 0:
                return {
                    "total_count": 0,
                    "earliest_date": None,
                    "latest_date": None,
                    "symbols_count": 0,
                    "periods": []
                }
            
            # 统计信息
            earliest_date = session.query(func.min(StockData.trade_date)).filter(
                query.whereclause
            ).scalar()
            
            latest_date = session.query(func.max(StockData.trade_date)).filter(
                query.whereclause
            ).scalar()
            
            symbols_count = session.query(func.count(func.distinct(StockData.symbol))).filter(
                query.whereclause
            ).scalar()
            
            periods = session.query(func.distinct(StockData.period)).filter(
                query.whereclause
            ).all()
            periods = [p[0] for p in periods]
            
            return {
                "total_count": total_count,
                "earliest_date": earliest_date,
                "latest_date": latest_date,
                "symbols_count": symbols_count,
                "periods": periods
            }
    
    def cleanup_old_data(self, period: str, keep_days: int) -> int:
        """清理旧数据"""
        cutoff_date = TimeUtils.get_current_date() - timedelta(days=keep_days)
        
        with get_db_context() as session:
            deleted_count = session.query(StockData).filter(
                and_(
                    StockData.period == period,
                    StockData.trade_date < cutoff_date
                )
            ).delete()
            
            logger.info(f"清理旧数据完成: {period}, 删除: {deleted_count} 条")
            return deleted_count
