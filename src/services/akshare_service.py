"""
AkShare数据获取服务

封装AkShare API调用，提供统一的数据获取接口。
"""

import time
from datetime import date, datetime
from typing import Optional, Dict, List, Any
import pandas as pd
import akshare as ak
from ..config import settings
from ..utils.logger import get_logger, LogTimer
from ..utils.time_utils import TimeUtils

logger = get_logger(__name__)


class AkShareService:
    """AkShare数据获取服务"""
    
    def __init__(self):
        self.timeout = settings.akshare.timeout
        self.retry_times = settings.akshare.retry_times
        self.retry_delay = settings.akshare.retry_delay
    
    def _retry_request(self, func, *args, **kwargs) -> Optional[pd.DataFrame]:
        """重试机制包装器"""
        last_error = None
        
        for attempt in range(self.retry_times):
            try:
                with LogTimer(f"AkShare请求: {func.__name__}", logger.name):
                    result = func(*args, **kwargs)
                    if result is not None and not result.empty:
                        logger.debug(f"AkShare请求成功: {func.__name__}, 数据量: {len(result)}")
                        return result
                    else:
                        logger.warning(f"AkShare返回空数据: {func.__name__}")
                        return pd.DataFrame()
            
            except Exception as e:
                last_error = e
                logger.warning(f"AkShare请求失败 (尝试 {attempt + 1}/{self.retry_times}): {str(e)}")
                
                if attempt < self.retry_times - 1:
                    time.sleep(self.retry_delay * (attempt + 1))
        
        logger.error(f"AkShare请求最终失败: {func.__name__}, 错误: {str(last_error)}")
        return None
    
    def get_stock_list(self) -> Optional[pd.DataFrame]:
        """获取股票列表"""
        def _get_stock_list():
            # 获取A股股票列表
            stock_list = []
            
            # 上海A股
            try:
                sh_stocks = ak.stock_info_a_code_name()
                if not sh_stocks.empty:
                    sh_stocks['market'] = 'SH'
                    stock_list.append(sh_stocks)
            except Exception as e:
                logger.warning(f"获取上海A股列表失败: {str(e)}")
            
            # 深圳A股
            try:
                sz_stocks = ak.stock_info_sz_name_code()
                if not sz_stocks.empty:
                    sz_stocks['market'] = 'SZ'
                    # 统一列名
                    if 'A股代码' in sz_stocks.columns:
                        sz_stocks = sz_stocks.rename(columns={'A股代码': 'code', 'A股简称': 'name'})
                    stock_list.append(sz_stocks)
            except Exception as e:
                logger.warning(f"获取深圳A股列表失败: {str(e)}")
            
            if stock_list:
                result = pd.concat(stock_list, ignore_index=True)
                # 标准化列名
                if 'code' in result.columns and 'name' in result.columns:
                    result = result[['code', 'name', 'market']]
                    result['symbol'] = result['code'] + '.' + result['market']
                    return result
            
            return pd.DataFrame()
        
        return self._retry_request(_get_stock_list)
    
    def get_stock_info(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取股票基本信息"""
        def _get_stock_info():
            # 解析股票代码
            code = symbol.split('.')[0] if '.' in symbol else symbol
            
            try:
                # 获取股票基本信息
                info = ak.stock_individual_info_em(symbol=code)
                if info is not None and not info.empty:
                    # 转换为字典格式
                    info_dict = {}
                    for _, row in info.iterrows():
                        info_dict[row['item']] = row['value']
                    
                    return {
                        'symbol': symbol,
                        'name': info_dict.get('股票简称', ''),
                        'industry': info_dict.get('所属行业', ''),
                        'sector': info_dict.get('所属板块', ''),
                        'list_date': self._parse_date(info_dict.get('上市时间', '')),
                    }
            except Exception as e:
                logger.warning(f"获取股票信息失败 {symbol}: {str(e)}")
            
            return None
        
        return self._retry_request(_get_stock_info)
    
    def get_stock_data(self, symbol: str, period: str = "1d", 
                      start_date: Optional[date] = None, 
                      end_date: Optional[date] = None) -> Optional[pd.DataFrame]:
        """获取股票历史数据"""
        
        # 设置默认时间范围
        if end_date is None:
            end_date = TimeUtils.get_current_date()
        if start_date is None:
            start_date, end_date = TimeUtils.get_period_range(period, end_date)
        
        def _get_stock_data():
            code = symbol.split('.')[0] if '.' in symbol else symbol
            start_str = TimeUtils.format_date_for_akshare(start_date)
            end_str = TimeUtils.format_date_for_akshare(end_date)
            
            try:
                if period == "1d":
                    # 日线数据
                    data = ak.stock_zh_a_hist(
                        symbol=code,
                        period="daily",
                        start_date=start_str,
                        end_date=end_str,
                        adjust="qfq"  # 前复权
                    )
                elif period == "1w":
                    # 周线数据
                    data = ak.stock_zh_a_hist(
                        symbol=code,
                        period="weekly",
                        start_date=start_str,
                        end_date=end_str,
                        adjust="qfq"
                    )
                elif period == "1M":
                    # 月线数据
                    data = ak.stock_zh_a_hist(
                        symbol=code,
                        period="monthly",
                        start_date=start_str,
                        end_date=end_str,
                        adjust="qfq"
                    )
                elif period in ["1m", "5m", "30m", "1h"]:
                    # 分钟级数据
                    period_map = {
                        "1m": "1",
                        "5m": "5", 
                        "30m": "30",
                        "1h": "60"
                    }
                    data = ak.stock_zh_a_hist_min_em(
                        symbol=code,
                        period=period_map[period],
                        start_date=start_str + " 09:30:00",
                        end_date=end_str + " 15:00:00",
                        adjust="qfq"
                    )
                else:
                    logger.error(f"不支持的周期: {period}")
                    return None
                
                if data is not None and not data.empty:
                    # 标准化列名
                    data = self._standardize_columns(data, period)
                    # 添加股票代码
                    data['symbol'] = symbol
                    data['period'] = period
                    
                    logger.info(f"获取股票数据成功: {symbol}, 周期: {period}, 数量: {len(data)}")
                    return data
                
            except Exception as e:
                logger.error(f"获取股票数据失败: {symbol}, 周期: {period}, 错误: {str(e)}")
                return None
        
        return self._retry_request(_get_stock_data)
    
    def _standardize_columns(self, data: pd.DataFrame, period: str) -> pd.DataFrame:
        """标准化数据列名"""
        # 常见的列名映射
        column_mapping = {
            '日期': 'date',
            '时间': 'datetime',
            '开盘': 'open',
            '收盘': 'close', 
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'pct_chg',
            '涨跌额': 'change',
            '换手率': 'turnover'
        }
        
        # 重命名列
        data = data.rename(columns=column_mapping)
        
        # 确保必要的列存在
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in data.columns:
                logger.warning(f"缺少必要列: {col}")
        
        # 处理时间列
        if 'datetime' in data.columns:
            data['trade_time'] = data['datetime'].apply(
                lambda x: TimeUtils.parse_akshare_datetime(str(x), period)
            )
        elif 'date' in data.columns:
            data['trade_time'] = data['date'].apply(
                lambda x: TimeUtils.parse_akshare_datetime(str(x), period)
            )
        else:
            # 使用索引作为时间
            data['trade_time'] = data.index.to_series().apply(
                lambda x: TimeUtils.parse_akshare_datetime(str(x), period)
            )
        
        # 确保数值类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount', 'pct_chg', 'turnover']
        for col in numeric_columns:
            if col in data.columns:
                data[col] = pd.to_numeric(data[col], errors='coerce')
        
        return data
    
    def _parse_date(self, date_str: str) -> Optional[date]:
        """解析日期字符串"""
        if not date_str or date_str == '-':
            return None
        
        try:
            return TimeUtils.parse_akshare_date(date_str)
        except Exception:
            return None
    
    def get_trading_calendar(self, year: int) -> Optional[List[date]]:
        """获取交易日历"""
        def _get_trading_calendar():
            try:
                calendar = ak.tool_trade_date_hist_sina()
                if calendar is not None and not calendar.empty:
                    # 筛选指定年份
                    calendar['trade_date'] = pd.to_datetime(calendar['trade_date'])
                    year_calendar = calendar[calendar['trade_date'].dt.year == year]
                    return year_calendar['trade_date'].dt.date.tolist()
            except Exception as e:
                logger.error(f"获取交易日历失败: {str(e)}")
            
            return None
        
        return self._retry_request(_get_trading_calendar)
