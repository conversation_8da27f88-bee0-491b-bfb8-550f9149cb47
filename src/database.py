"""
数据库连接和会话管理

使用SQLAlchemy和psycopg进行数据库连接管理。
"""

from contextlib import contextmanager
from typing import Generator, Optional
from sqlalchemy import create_engine, Engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool

from .config import settings
from .models.base import Base
from .utils.logger import get_logger

logger = get_logger(__name__)


class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self):
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
    
    def create_engine(self) -> Engine:
        """创建数据库引擎"""
        if self._engine is not None:
            return self._engine
        
        # 数据库连接配置
        engine_config = {
            "url": settings.database.url,
            "poolclass": QueuePool,
            "pool_size": settings.database.pool_size,
            "max_overflow": settings.database.max_overflow,
            "pool_pre_ping": True,  # 连接前检查
            "pool_recycle": 3600,   # 1小时回收连接
            "echo": settings.app.debug,  # 调试模式下显示SQL
        }
        
        self._engine = create_engine(**engine_config)
        
        # 添加连接事件监听器
        self._setup_engine_events(self._engine)
        
        logger.info(f"数据库引擎已创建 - {settings.database.host}:{settings.database.port}/{settings.database.name}")
        return self._engine
    
    def _setup_engine_events(self, engine: Engine) -> None:
        """设置引擎事件监听器"""
        
        @event.listens_for(engine, "connect")
        def set_sqlite_pragma(dbapi_connection, connection_record):
            """连接时设置数据库参数"""
            if "postgresql" in str(engine.url):
                # PostgreSQL特定设置
                with dbapi_connection.cursor() as cursor:
                    cursor.execute("SET timezone TO 'Asia/Shanghai'")
                    cursor.execute("SET statement_timeout = '30s'")
        
        @event.listens_for(engine, "checkout")
        def receive_checkout(dbapi_connection, connection_record, connection_proxy):
            """连接检出时的处理"""
            logger.debug("数据库连接已检出")
        
        @event.listens_for(engine, "checkin")
        def receive_checkin(dbapi_connection, connection_record):
            """连接检入时的处理"""
            logger.debug("数据库连接已检入")
    
    def create_session_factory(self) -> sessionmaker:
        """创建会话工厂"""
        if self._session_factory is not None:
            return self._session_factory
        
        engine = self.create_engine()
        self._session_factory = sessionmaker(
            bind=engine,
            autocommit=False,
            autoflush=False,
            expire_on_commit=False
        )
        
        logger.info("数据库会话工厂已创建")
        return self._session_factory
    
    def get_session(self) -> Session:
        """获取数据库会话"""
        session_factory = self.create_session_factory()
        return session_factory()
    
    @contextmanager
    def get_session_context(self) -> Generator[Session, None, None]:
        """获取数据库会话上下文管理器"""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库会话异常: {str(e)}")
            raise
        finally:
            session.close()
    
    def init_database(self, drop_all: bool = False) -> None:
        """初始化数据库"""
        engine = self.create_engine()
        
        if drop_all:
            logger.warning("删除所有数据库表")
            Base.metadata.drop_all(bind=engine)
        
        logger.info("创建数据库表")
        Base.metadata.create_all(bind=engine)
        
        # 创建索引和约束
        self._create_additional_constraints(engine)
        
        logger.info("数据库初始化完成")
    
    def _create_additional_constraints(self, engine: Engine) -> None:
        """创建额外的约束和触发器"""
        with engine.connect() as conn:
            # 创建更新时间戳函数
            conn.execute("""
                CREATE OR REPLACE FUNCTION update_updated_at_column()
                RETURNS TRIGGER AS $$
                BEGIN
                    NEW.updated_at = CURRENT_TIMESTAMP;
                    RETURN NEW;
                END;
                $$ language 'plpgsql';
            """)
            
            # 为stock_info表添加触发器
            conn.execute("""
                DROP TRIGGER IF EXISTS update_stock_info_updated_at ON stock_info;
                CREATE TRIGGER update_stock_info_updated_at 
                    BEFORE UPDATE ON stock_info 
                    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            """)
            
            # 为stock_data表添加触发器
            conn.execute("""
                DROP TRIGGER IF EXISTS update_stock_data_updated_at ON stock_data;
                CREATE TRIGGER update_stock_data_updated_at 
                    BEFORE UPDATE ON stock_data 
                    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
            """)
            
            # 添加数据约束
            conn.execute("""
                ALTER TABLE stock_data 
                DROP CONSTRAINT IF EXISTS chk_positive_prices;
                ALTER TABLE stock_data ADD CONSTRAINT chk_positive_prices 
                CHECK (open_price > 0 AND high_price > 0 AND low_price > 0 AND close_price > 0);
            """)
            
            conn.execute("""
                ALTER TABLE stock_data 
                DROP CONSTRAINT IF EXISTS chk_price_logic;
                ALTER TABLE stock_data ADD CONSTRAINT chk_price_logic 
                CHECK (high_price >= low_price);
            """)
            
            conn.execute("""
                ALTER TABLE stock_data 
                DROP CONSTRAINT IF EXISTS chk_positive_volume;
                ALTER TABLE stock_data ADD CONSTRAINT chk_positive_volume 
                CHECK (volume >= 0 AND amount >= 0);
            """)
            
            conn.commit()
            logger.info("数据库约束和触发器创建完成")
    
    def check_connection(self) -> bool:
        """检查数据库连接"""
        try:
            engine = self.create_engine()
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            logger.info("数据库连接正常")
            return True
        except Exception as e:
            logger.error(f"数据库连接失败: {str(e)}")
            return False
    
    def get_database_info(self) -> dict:
        """获取数据库信息"""
        try:
            engine = self.create_engine()
            with engine.connect() as conn:
                result = conn.execute("""
                    SELECT 
                        version() as version,
                        current_database() as database,
                        current_user as user,
                        inet_server_addr() as host,
                        inet_server_port() as port
                """).fetchone()
                
                return {
                    "version": result.version,
                    "database": result.database,
                    "user": result.user,
                    "host": result.host,
                    "port": result.port,
                    "url": str(engine.url).replace(f":{settings.database.password}", ":***")
                }
        except Exception as e:
            logger.error(f"获取数据库信息失败: {str(e)}")
            return {}
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self._engine:
            self._engine.dispose()
            self._engine = None
            self._session_factory = None
            logger.info("数据库连接已关闭")


# 全局数据库管理器实例
db_manager = DatabaseManager()


def get_engine() -> Engine:
    """获取数据库引擎"""
    return db_manager.create_engine()


def get_session_factory() -> sessionmaker:
    """获取会话工厂"""
    return db_manager.create_session_factory()


def get_db_session() -> Session:
    """获取数据库会话"""
    return db_manager.get_session()


@contextmanager
def get_db_context() -> Generator[Session, None, None]:
    """获取数据库会话上下文管理器"""
    with db_manager.get_session_context() as session:
        yield session


def init_database(drop_all: bool = False) -> None:
    """初始化数据库"""
    db_manager.init_database(drop_all=drop_all)


def check_database_connection() -> bool:
    """检查数据库连接"""
    return db_manager.check_connection()


def get_database_info() -> dict:
    """获取数据库信息"""
    return db_manager.get_database_info()
