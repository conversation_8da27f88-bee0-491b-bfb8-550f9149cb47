"""
时间工具模块

提供时间处理相关的工具函数。
"""

from datetime import datetime, date, timedelta
from typing import List, Tuple, Optional

try:
    import pytz
except ImportError:
    pytz = None

from ..config import settings


class TimeUtils:
    """时间工具类"""
    
    # 中国时区
    if pytz:
        CHINA_TZ = pytz.timezone(settings.app.timezone)
    else:
        CHINA_TZ = None
    
    @classmethod
    def get_current_time(cls) -> datetime:
        """获取当前时间（中国时区）"""
        if cls.CHINA_TZ:
            return datetime.now(cls.CHINA_TZ)
        else:
            return datetime.now()
    
    @classmethod
    def get_current_date(cls) -> date:
        """获取当前日期"""
        return cls.get_current_time().date()
    
    @classmethod
    def get_trading_days(cls, start_date: date, end_date: date) -> List[date]:
        """获取交易日列表（排除周末）"""
        trading_days = []
        current_date = start_date
        
        while current_date <= end_date:
            # 排除周末（周六=5, 周日=6）
            if current_date.weekday() < 5:
                trading_days.append(current_date)
            current_date += timedelta(days=1)
        
        return trading_days
    
    @classmethod
    def is_trading_day(cls, check_date: date) -> bool:
        """判断是否为交易日（简单版本，只排除周末）"""
        return check_date.weekday() < 5
    
    @classmethod
    def get_last_trading_day(cls, from_date: Optional[date] = None) -> date:
        """获取最近的交易日"""
        if from_date is None:
            from_date = cls.get_current_date()
        
        current_date = from_date
        while not cls.is_trading_day(current_date):
            current_date -= timedelta(days=1)
        
        return current_date
    
    @classmethod
    def get_next_trading_day(cls, from_date: Optional[date] = None) -> date:
        """获取下一个交易日"""
        if from_date is None:
            from_date = cls.get_current_date()
        
        current_date = from_date + timedelta(days=1)
        while not cls.is_trading_day(current_date):
            current_date += timedelta(days=1)
        
        return current_date
    
    @classmethod
    def get_period_range(cls, period: str, end_date: Optional[date] = None) -> Tuple[date, date]:
        """根据周期获取时间范围"""
        if end_date is None:
            end_date = cls.get_current_date()
        
        if period == "1d":
            # 日线：最近1年
            start_date = end_date - timedelta(days=365)
        elif period == "1w":
            # 周线：最近2年
            start_date = end_date - timedelta(days=730)
        elif period == "1M":
            # 月线：最近5年
            start_date = end_date - timedelta(days=1825)
        elif period == "1y":
            # 年线：最近20年
            start_date = end_date - timedelta(days=7300)
        elif period in ["1h", "30m"]:
            # 小时线：最近3个月
            start_date = end_date - timedelta(days=90)
        elif period in ["5m", "1m"]:
            # 分钟线：最近1个月
            start_date = end_date - timedelta(days=30)
        else:
            # 默认：最近1年
            start_date = end_date - timedelta(days=365)
        
        return start_date, end_date
    
    @classmethod
    def format_date_for_akshare(cls, date_obj: date) -> str:
        """格式化日期为AkShare需要的格式"""
        return date_obj.strftime("%Y%m%d")
    
    @classmethod
    def parse_akshare_date(cls, date_str: str) -> date:
        """解析AkShare返回的日期字符串"""
        if isinstance(date_str, str):
            if len(date_str) == 8:  # YYYYMMDD
                return datetime.strptime(date_str, "%Y%m%d").date()
            elif len(date_str) == 10:  # YYYY-MM-DD
                return datetime.strptime(date_str, "%Y-%m-%d").date()
        return date_str
    
    @classmethod
    def parse_akshare_datetime(cls, datetime_str: str, period: str = "1d") -> datetime:
        """解析AkShare返回的时间字符串"""
        if isinstance(datetime_str, str):
            # 尝试不同的时间格式
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d %H:%M",
                "%Y-%m-%d",
                "%Y%m%d %H:%M:%S",
                "%Y%m%d %H:%M",
                "%Y%m%d"
            ]
            
            for fmt in formats:
                try:
                    dt = datetime.strptime(datetime_str, fmt)
                    # 如果是日期格式，根据周期设置时间
                    if fmt.endswith("%d"):
                        if period in ["1d", "1w", "1M", "1y"]:
                            dt = dt.replace(hour=15, minute=0, second=0)  # 收盘时间
                        else:
                            dt = dt.replace(hour=9, minute=30, second=0)  # 开盘时间
                    
                    # 设置时区
                    return cls.CHINA_TZ.localize(dt)
                except ValueError:
                    continue
        
        # 如果解析失败，返回当前时间
        return cls.get_current_time()
    
    @classmethod
    def get_market_hours(cls) -> Tuple[datetime, datetime]:
        """获取当日交易时间"""
        current_date = cls.get_current_date()
        
        # 上午：9:30-11:30
        morning_start = cls.CHINA_TZ.localize(
            datetime.combine(current_date, datetime.min.time().replace(hour=9, minute=30))
        )
        morning_end = cls.CHINA_TZ.localize(
            datetime.combine(current_date, datetime.min.time().replace(hour=11, minute=30))
        )
        
        # 下午：13:00-15:00
        afternoon_start = cls.CHINA_TZ.localize(
            datetime.combine(current_date, datetime.min.time().replace(hour=13, minute=0))
        )
        afternoon_end = cls.CHINA_TZ.localize(
            datetime.combine(current_date, datetime.min.time().replace(hour=15, minute=0))
        )
        
        return (morning_start, morning_end, afternoon_start, afternoon_end)
    
    @classmethod
    def is_market_open(cls, check_time: Optional[datetime] = None) -> bool:
        """判断是否在交易时间内"""
        if check_time is None:
            check_time = cls.get_current_time()
        
        # 检查是否为交易日
        if not cls.is_trading_day(check_time.date()):
            return False
        
        morning_start, morning_end, afternoon_start, afternoon_end = cls.get_market_hours()
        
        # 检查是否在交易时间内
        return (morning_start <= check_time <= morning_end) or \
               (afternoon_start <= check_time <= afternoon_end)
    
    @classmethod
    def get_period_minutes(cls, period: str) -> int:
        """获取周期对应的分钟数"""
        period_minutes = {
            "1m": 1,
            "5m": 5,
            "30m": 30,
            "1h": 60,
            "1d": 1440,  # 24 * 60
            "1w": 10080,  # 7 * 24 * 60
            "1M": 43200,  # 30 * 24 * 60
            "1y": 525600  # 365 * 24 * 60
        }
        return period_minutes.get(period, 1440)
