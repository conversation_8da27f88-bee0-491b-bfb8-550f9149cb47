"""
日志系统模块

使用loguru进行日志管理，提供结构化日志记录功能。
"""

import sys
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger
from rich.console import Console
from rich.logging import RichHandler

from ..config import settings


class LoggerManager:
    """日志管理器"""
    
    def __init__(self):
        self.console = Console()
        self._configured = False
    
    def configure(self, 
                  level: Optional[str] = None,
                  file_path: Optional[str] = None,
                  rotation: Optional[str] = None,
                  retention: Optional[str] = None,
                  format_string: Optional[str] = None) -> None:
        """配置日志系统"""
        
        if self._configured:
            return
        
        # 移除默认处理器
        logger.remove()
        
        # 使用配置或默认值
        level = level or settings.log.level
        file_path = file_path or settings.log.file_path
        rotation = rotation or settings.log.rotation
        retention = retention or settings.log.retention
        format_string = format_string or settings.log.format
        
        # 确保日志目录存在
        log_file = Path(file_path)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 控制台处理器（带颜色）
        logger.add(
            sys.stdout,
            level=level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
                   "<level>{level: <8}</level> | "
                   "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | "
                   "<level>{message}</level>",
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # 文件处理器
        logger.add(
            file_path,
            level=level,
            format=format_string,
            rotation=rotation,
            retention=retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        
        # 错误文件处理器
        error_file = log_file.parent / f"{log_file.stem}_error{log_file.suffix}"
        logger.add(
            str(error_file),
            level="ERROR",
            format=format_string,
            rotation=rotation,
            retention=retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        
        self._configured = True
        logger.info(f"日志系统已配置 - 级别: {level}, 文件: {file_path}")
    
    def get_logger(self, name: str = None) -> "logger":
        """获取日志记录器"""
        if not self._configured:
            self.configure()
        
        if name:
            return logger.bind(name=name)
        return logger
    
    def log_function_call(self, func_name: str, args: tuple = None, kwargs: dict = None):
        """记录函数调用"""
        args_str = f"args={args}" if args else ""
        kwargs_str = f"kwargs={kwargs}" if kwargs else ""
        params = ", ".join(filter(None, [args_str, kwargs_str]))
        logger.debug(f"调用函数 {func_name}({params})")
    
    def log_database_operation(self, operation: str, table: str, count: int = None, **kwargs):
        """记录数据库操作"""
        extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
        count_str = f", 影响行数: {count}" if count is not None else ""
        logger.info(f"数据库操作: {operation} - 表: {table}{count_str}" + 
                   (f", {extra_info}" if extra_info else ""))
    
    def log_akshare_request(self, symbol: str, period: str, start_date: str = None, 
                           end_date: str = None, success: bool = True, error: str = None):
        """记录AkShare请求"""
        date_range = ""
        if start_date and end_date:
            date_range = f", 时间范围: {start_date} ~ {end_date}"
        elif start_date:
            date_range = f", 开始时间: {start_date}"
        
        if success:
            logger.info(f"AkShare请求成功 - 股票: {symbol}, 周期: {period}{date_range}")
        else:
            logger.error(f"AkShare请求失败 - 股票: {symbol}, 周期: {period}{date_range}, 错误: {error}")
    
    def log_data_processing(self, operation: str, count: int, **kwargs):
        """记录数据处理"""
        extra_info = ", ".join([f"{k}={v}" for k, v in kwargs.items()])
        logger.info(f"数据处理: {operation} - 处理数量: {count}" + 
                   (f", {extra_info}" if extra_info else ""))


# 全局日志管理器实例
log_manager = LoggerManager()


def get_logger(name: str = None) -> "logger":
    """获取日志记录器的便捷函数"""
    return log_manager.get_logger(name)


def configure_logging(**kwargs) -> None:
    """配置日志系统的便捷函数"""
    log_manager.configure(**kwargs)


# 装饰器：自动记录函数调用
def log_function(logger_name: str = None):
    """装饰器：自动记录函数调用和异常"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_logger = get_logger(logger_name or func.__module__)
            func_name = f"{func.__module__}.{func.__name__}"
            
            try:
                func_logger.debug(f"开始执行函数: {func_name}")
                result = func(*args, **kwargs)
                func_logger.debug(f"函数执行成功: {func_name}")
                return result
            except Exception as e:
                func_logger.error(f"函数执行失败: {func_name}, 错误: {str(e)}")
                raise
        
        return wrapper
    return decorator


# 上下文管理器：记录操作耗时
class LogTimer:
    """记录操作耗时的上下文管理器"""
    
    def __init__(self, operation_name: str, logger_name: str = None):
        self.operation_name = operation_name
        self.logger = get_logger(logger_name)
        self.start_time = None
    
    def __enter__(self):
        import time
        self.start_time = time.time()
        self.logger.info(f"开始执行: {self.operation_name}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        duration = time.time() - self.start_time
        
        if exc_type is None:
            self.logger.info(f"执行完成: {self.operation_name}, 耗时: {duration:.2f}秒")
        else:
            self.logger.error(f"执行失败: {self.operation_name}, 耗时: {duration:.2f}秒, 错误: {exc_val}")


# 初始化日志系统
configure_logging()
