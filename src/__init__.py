"""
股票数据管理系统

一个现代化的Python股票数据管理系统，支持从AkShare获取多时间级别的股票数据，
并存储到PostgreSQL数据库中。

主要功能：
- 支持年、月、周、日、小时、分钟级别的股票数据获取
- 使用SQLAlchemy ORM进行数据库操作
- 支持数据去重和增量更新
- 提供完整的日志记录和错误处理
- 支持定时任务和手动更新
"""

__version__ = "1.0.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"

from .config import settings
from .database import get_db_session, init_database
from .models import StockInfo, StockData
from .services import StockDataService

__all__ = [
    "settings",
    "get_db_session",
    "init_database", 
    "StockInfo",
    "StockData",
    "StockDataService",
]
