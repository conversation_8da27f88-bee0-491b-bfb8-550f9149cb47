"""
配置模块测试
"""

import pytest
from unittest.mock import patch
from src.config import Settings, DatabaseSettings, LogSettings


class TestDatabaseSettings:
    """数据库配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        db_settings = DatabaseSettings(password="test_password")
        
        assert db_settings.host == "localhost"
        assert db_settings.port == 5432
        assert db_settings.name == "stock_db"
        assert db_settings.user == "postgres"
        assert db_settings.password == "test_password"
        assert db_settings.pool_size == 10
        assert db_settings.max_overflow == 20
    
    def test_url_generation(self):
        """测试URL生成"""
        db_settings = DatabaseSettings(
            host="testhost",
            port=5433,
            name="testdb",
            user="testuser",
            password="testpass"
        )
        
        expected_url = "postgresql+psycopg://testuser:testpass@testhost:5433/testdb"
        assert db_settings.url == expected_url
    
    def test_async_url_generation(self):
        """测试异步URL生成"""
        db_settings = DatabaseSettings(
            host="testhost",
            port=5433,
            name="testdb",
            user="testuser",
            password="testpass"
        )
        
        expected_url = "postgresql+asyncpg://testuser:testpass@testhost:5433/testdb"
        assert db_settings.async_url == expected_url


class TestLogSettings:
    """日志配置测试"""
    
    def test_default_values(self):
        """测试默认值"""
        log_settings = LogSettings()
        
        assert log_settings.level == "INFO"
        assert log_settings.file_path == "logs/stock_db.log"
        assert log_settings.rotation == "1 day"
        assert log_settings.retention == "30 days"
    
    def test_level_validation(self):
        """测试日志级别验证"""
        # 有效级别
        valid_levels = ["TRACE", "DEBUG", "INFO", "SUCCESS", "WARNING", "ERROR", "CRITICAL"]
        for level in valid_levels:
            log_settings = LogSettings(level=level)
            assert log_settings.level == level
        
        # 无效级别
        with pytest.raises(ValueError):
            LogSettings(level="INVALID")


class TestSettings:
    """主配置测试"""
    
    def test_settings_initialization(self):
        """测试配置初始化"""
        settings = Settings()
        
        assert isinstance(settings.database, DatabaseSettings)
        assert isinstance(settings.log, LogSettings)
        assert settings.app.name == "stock-db"
        assert settings.app.version == "1.0.0"
    
    @patch.dict('os.environ', {
        'DB_HOST': 'test_host',
        'DB_PORT': '5433',
        'LOG_LEVEL': 'DEBUG'
    })
    def test_environment_variables(self):
        """测试环境变量加载"""
        settings = Settings()
        
        assert settings.database.host == "test_host"
        assert settings.database.port == 5433
        assert settings.log.level == "DEBUG"
