# 股票数据管理系统 (Stock-DB)

一个现代化的Python股票数据管理系统，支持从AkShare获取多时间级别的股票数据，并存储到PostgreSQL数据库中。

## 🚀 特性

- **多时间级别数据支持**: 支持1分钟到年线的8个时间级别
- **现代化架构**: SQLAlchemy 2.0 + Pydantic + psycopg
- **高性能设计**: 并发数据获取、批量处理、连接池管理
- **完整的日志系统**: 基于loguru的结构化日志记录
- **数据质量保证**: 完整的数据验证、去重和清洗机制
- **灵活的更新策略**: 支持增量更新、批量更新、定时任务
- **简洁的命令行工具**: 统一的CLI接口，易于使用
- **强大的容错机制**: 自动重试、错误处理、状态监控
- **Conda环境管理**: 开箱即用的环境配置

## 📋 支持的数据类型

| 周期 | 描述 | 数据来源 | 更新频率 |
|------|------|----------|----------|
| 1m | 1分钟 | AkShare | 实时（交易时间） |
| 5m | 5分钟 | AkShare | 实时（交易时间） |
| 30m | 30分钟 | AkShare | 实时（交易时间） |
| 1h | 1小时 | AkShare | 实时（交易时间） |
| 1d | 日线 | AkShare | 每日收盘后 |
| 1w | 周线 | AkShare | 每周末 |
| 1M | 月线 | AkShare | 每月末 |
| 1y | 年线 | AkShare | 每年末 |

## 🛠️ 技术栈

- **Python 3.9+**: 主要开发语言
- **PostgreSQL**: 数据库存储
- **SQLAlchemy 2.0**: ORM框架
- **psycopg**: PostgreSQL连接器
- **AkShare**: 股票数据源
- **Pydantic**: 配置和数据验证
- **loguru**: 日志系统
- **pandas**: 数据处理

## 📦 安装

### 快速开始

#### 方式1：一键安装（推荐）
```bash
# 1. 克隆项目
git clone <repository-url>
cd stock-db

# 2. 运行安装脚本
./scripts/install.sh

# 3. 开始使用
conda activate stock-db
python scripts/run.py update-data --symbol 000001.SZ --periods 1d
```

#### 方式2：手动安装
```bash
# 1. 克隆项目
git clone <repository-url>
cd stock-db

# 2. 创建并激活conda环境
conda env create -f environment.yml
conda activate stock-db

# 3. 配置数据库连接（编辑.env文件）
cp .env.example .env

# 4. 初始化数据库
python scripts/run.py init-db

# 5. 开始使用
python scripts/run.py update-stocks
python scripts/run.py update-data --symbol 000001.SZ --periods 1d
```

### 详细安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd stock-db
```

### 2. 创建Conda环境
```bash
# 创建环境
conda env create -f environment.yml

# 激活环境
conda activate stock-db
```

### 3. 配置环境变量
```bash
# 创建环境变量文件
cp .env.example .env

# 编辑 .env 文件，配置数据库连接信息
# 主要需要配置以下参数：
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=stock_db
# DB_USER=your_username
# DB_PASSWORD=your_password
```

### 4. 初始化数据库
```bash
# 检查数据库连接
python scripts/run.py check-db

# 初始化数据库表结构
python scripts/run.py init-db

# 更新股票列表
python scripts/run.py update-stocks
```

## ⚙️ 配置

主要配置项在 `.env` 文件中：

```env
# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=stock_db
DB_USER=postgres
DB_PASSWORD=your_password

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/stock_db.log

# AkShare配置
AKSHARE_TIMEOUT=30
AKSHARE_RETRY_TIMES=3

# 更新配置
UPDATE_SCHEDULE_TIME=18:00
UPDATE_BATCH_SIZE=100
UPDATE_CONCURRENT_LIMIT=5
```

## 🎯 使用指南

### 基本命令

所有命令都通过 `python scripts/run.py` 执行：

```bash
# 查看帮助
python scripts/run.py --help

# 检查数据库连接
python scripts/run.py check-db

# 初始化数据库
python scripts/run.py init-db

# 更新股票列表
python scripts/run.py update-stocks

# 查看统计信息
python scripts/run.py stats
```

### 数据更新

```bash
# 更新单只股票的日线数据
python scripts/run.py update-data --symbol 000001.SZ --periods 1d

# 批量更新多个周期数据
python scripts/run.py update-data --periods 1d 1h 30m

# 增量更新（推荐日常使用）
python scripts/run.py update-data --incremental

# 强制更新指定时间范围
python scripts/run.py update-data --symbol 000001.SZ --start-date 2024-01-01 --end-date 2024-01-31
```

### 数据查询

```bash
# 查询股票数据
python scripts/run.py query 000001.SZ --period 1d --limit 10

# 查询指定时间范围
python scripts/run.py query 000001.SZ --start-date 2024-01-01 --end-date 2024-01-31

# 查询不同周期数据
python scripts/run.py query 000001.SZ --period 1h --limit 20
```

### 定时任务

```bash
# 启动定时任务（前台运行）
python scripts/run.py schedule --time 18:00

# 设置系统定时任务
chmod +x scripts/setup_cron.sh
./scripts/setup_cron.sh
```

## 📚 API使用

### Python API示例

```python
from src import StockDataService, UpdateService

# 创建服务实例
stock_service = StockDataService()
update_service = UpdateService()

# 获取股票数据
data_list = stock_service.get_stock_data(
    symbol="000001.SZ",
    period="1d",
    limit=100
)

# 更新单只股票
result = update_service.update_single_stock(
    symbol="000001.SZ",
    periods=["1d", "1h"]
)

# 批量更新
result = update_service.update_all_stocks(
    periods=["1d"],
    force_update=False
)
```

### 数据库直接查询

```python
from src.database import get_db_context
from src.models import StockData

with get_db_context() as session:
    # 查询最新的日线数据
    latest_data = session.query(StockData).filter(
        StockData.symbol == "000001.SZ",
        StockData.period == "1d"
    ).order_by(StockData.trade_date.desc()).limit(10).all()

    for data in latest_data:
        print(f"{data.trade_date}: {data.close_price}")
```

## 🗂️ 项目结构

```
stock-db/
├── src/                  # 主要源代码
│   ├── __init__.py
│   ├── config.py         # 配置管理
│   ├── database.py       # 数据库连接
│   ├── cli.py           # 命令行接口
│   ├── models/          # 数据模型
│   │   ├── __init__.py
│   │   ├── base.py      # 基础模型
│   │   ├── stock_info.py # 股票信息模型
│   │   ├── stock_data.py # 股票数据模型
│   │   └── update_log.py # 更新日志模型
│   ├── services/        # 业务服务
│   │   ├── __init__.py
│   │   ├── akshare_service.py    # AkShare数据获取
│   │   ├── stock_data_service.py # 股票数据服务
│   │   └── update_service.py     # 更新服务
│   └── utils/           # 工具模块
│       ├── __init__.py
│       ├── logger.py    # 日志工具
│       └── time_utils.py # 时间工具
├── scripts/             # 脚本文件
│   ├── run.py          # 主启动脚本
│   ├── install.sh      # 安装脚本
│   ├── daily_update.py # 每日更新脚本
│   └── setup_cron.sh   # 定时任务设置
├── docs/                # 文档
│   ├── database_design.md # 数据库设计文档
│   ├── data_dictionary.md # 数据字典
│   ├── deployment_guide.md # 部署指南
│   ├── quick_start.md   # 快速开始指南
│   └── PROJECT_SUMMARY.md # 项目总结
├── tests/               # 测试文件
├── environment.yml     # Conda环境配置
├── requirements.txt    # 依赖列表
├── pyproject.toml     # 项目配置
├── Makefile           # 便捷命令
└── README.md          # 项目说明
```

## 🔧 开发

### 安装开发依赖
```bash
pip install -e ".[dev]"
```

### 代码格式化
```bash
black src/ tests/
```

### 类型检查
```bash
mypy src/
```

### 运行测试
```bash
pytest tests/ --cov=src
```

### 生成文档
```bash
cd docs/
sphinx-build -b html . _build/html
```

## 📊 监控和维护

### 查看日志
```bash
# 查看应用日志
tail -f logs/stock_db.log

# 查看定时任务日志
tail -f logs/daily_update.log
```

### 数据库维护
```bash
# 查看数据统计
python scripts/run.py stats

# 检查数据库连接
python scripts/run.py check-db

# 重新初始化数据库（谨慎使用）
python scripts/run.py init-db --drop-all
```

## 🔧 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查PostgreSQL服务状态
   sudo systemctl status postgresql

   # 检查配置文件
   cat .env | grep DB_

   # 测试连接
   python scripts/run.py check-db
   ```

2. **conda环境问题**
   ```bash
   # 重新创建环境
   conda env remove -n stock-db
   conda env create -f environment.yml
   conda activate stock-db
   ```

3. **权限错误**
   ```bash
   # 确保脚本有执行权限
   chmod +x scripts/*.sh
   chmod +x run.py
   ```

4. **依赖包问题**
   ```bash
   # 更新环境
   conda env update -f environment.yml

   # 或重新安装
   pip install -r requirements.txt
   ```

### 性能优化建议

1. **数据库优化**
   - 定期分析查询性能
   - 考虑数据分区
   - 监控索引使用情况

2. **数据更新优化**
   - 使用增量更新而非全量更新
   - 调整并发限制参数
   - 在非交易时间进行大批量更新

3. **存储优化**
   - 定期清理过期的分钟级数据
   - 压缩历史数据
   - 监控磁盘空间使用

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 📞 支持

如有问题，请提交Issue或联系维护者。
