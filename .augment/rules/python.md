---
type: "manual"
---

# Python工程师开发规范

## 代码风格

### PEP 8 规范

- 使用4个空格进行缩进，不使用Tab
- 每行代码不超过79个字符
- 使用空行分隔函数和类，以及函数内的逻辑块
- 导入应当分行，按照标准库、第三方库、本地库的顺序排列
- 使用空格围绕操作符和逗号后面

### 命名规范

- 类名使用驼峰命名法（CamelCase）
- 函数和变量名使用小写字母，单词之间用下划线连接（snake_case）
- 常量使用大写字母，单词之间用下划线连接（UPPER_SNAKE_CASE）
- 私有属性和方法以单下划线开头（_private_attr）
- 内部属性和方法以双下划线开头（__internal_attr）

### 注释规范

- 使用文档字符串（docstring）为模块、类、函数提供文档
- 使用Google风格或NumPy风格的docstring格式
- 注释应当解释为什么这样做，而不是做了什么
- 保持注释与代码的同步更新

## 项目结构

### 目录结构

```
project_name/
├── .env.example          # 环境变量示例文件
├── .gitignore            # Git忽略文件
├── README.md             # 项目说明文档
├── requirements.txt      # 依赖包列表
├── setup.py              # 安装脚本
├── docs/                 # 文档目录
├── tests/                # 测试目录
│   ├── __init__.py
│   ├── test_module1.py
│   └── test_module2.py
├── project_name/         # 主代码目录
│   ├── __init__.py       # 包初始化文件
│   ├── main.py           # 主入口文件
│   ├── config.py         # 配置文件
│   ├── module1/          # 模块目录
│   │   ├── __init__.py
│   │   └── module1.py
│   └── utils/            # 工具函数目录
│       ├── __init__.py
│       └── helpers.py
└── scripts/              # 脚本目录
    └── data_processor.py
```

### 模块划分

- 按照功能划分模块，保持模块的单一职责
- 避免循环导入
- 使用相对导入或绝对导入，保持一致性

## 编码最佳实践

### 函数设计

- 函数应当短小精悍，只做一件事
- 参数数量不宜过多，通常不超过5个
- 使用关键字参数提高可读性
- 返回值类型应当一致，避免返回不同类型

### 异常处理

- 只捕获预期的异常，避免捕获所有异常
- 使用finally子句进行清理操作
- 自定义异常应当继承自Exception
- 提供有意义的错误信息

### 资源管理

- 使用上下文管理器（with语句）管理资源
- 显式关闭文件、网络连接等资源
- 注意内存泄漏，特别是在长时间运行的应用中

### 并发编程

- 优先使用高级并发库（concurrent.futures）而非直接使用线程
- 对于IO密集型任务，考虑使用asyncio
- 对于CPU密集型任务，考虑使用multiprocessing
- 注意线程安全问题，使用锁或其他同步原语

## 测试规范

### 单元测试

- 使用pytest或unittest框架
- 测试覆盖率目标不低于80%
- 测试应当独立，不依赖于其他测试的执行顺序
- 使用mock对象隔离外部依赖

### 集成测试

- 测试组件之间的交互
- 使用测试数据库或测试环境
- 测试各种边界条件和错误情况

### 性能测试

- 使用性能分析工具（如cProfile）
- 设置基准测试，监控性能变化
- 关注关键路径的性能优化

## 版本控制

### Git工作流

- 使用feature分支开发新功能
- 使用pull request进行代码审查
- 主分支应当始终保持可部署状态
- 使用语义化版本号（Semantic Versioning）

### 提交信息

- 使用清晰、描述性的提交信息
- 遵循约定式提交规范（Conventional Commits）
- 每个提交应当是逻辑上完整的变更

## 文档规范

### README

- 包含项目简介、安装说明、使用示例
- 说明依赖项和兼容性要求
- 提供贡献指南和许可证信息

### API文档

- 使用Sphinx或MkDocs生成API文档
- 文档应当与代码保持同步
- 提供示例代码和使用场景

## 安全最佳实践

- 不在代码中硬编码敏感信息（密码、API密钥等）
- 使用环境变量或配置文件存储敏感信息
- 对用户输入进行验证和清洗
- 使用参数化查询防止SQL注入
- 定期更新依赖包，修复安全漏洞

## 性能优化

- 使用性能分析工具找出瓶颈
- 优先优化算法和数据结构，而非微优化
- 考虑使用缓存减少重复计算
- 对于大数据处理，考虑使用NumPy、Pandas等专业库
- 使用生成器处理大型数据集，避免一次性加载全部数据

## 代码审查清单

- 代码是否符合PEP 8规范
- 是否有适当的注释和文档
- 是否有充分的测试覆盖
- 是否处理了异常情况
- 是否有潜在的安全问题
- 是否有性能问题
- 是否有重复代码可以重构

## 持续集成/持续部署

- 使用CI/CD工具（如GitHub Actions、Jenkins）
- 自动化测试、构建和部署流程
- 设置代码质量检查（如flake8、pylint）
- 使用类型检查工具（如mypy）

## 依赖管理

- 使用虚拟环境隔离项目依赖
- 明确指定依赖版本，避免使用最新版本（^或~）
- 使用requirements.txt或setup.py管理依赖
- 考虑使用Poetry或Pipenv等现代依赖管理工具

## 日志规范

- 使用标准库logging模块
- 设置适当的日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
- 包含上下文信息（时间戳、模块名、函数名等）
- 敏感信息不应出现在日志中

## 环境配置

- 使用.env文件和python-dotenv管理环境变量
- 区分开发、测试和生产环境
- 配置应当可以通过环境变量覆盖
- 提供默认配置和配置验证

## 代码质量工具

- flake8：代码风格检查
- black：代码格式化
- isort：导入排序
- mypy：类型检查
- bandit：安全检查
- coverage：测试覆盖率

## 总结

遵循这些规范和最佳实践，可以帮助团队编写出更加可维护、可测试和高质量的Python代码。规范不是一成不变的，应当根据项目需求和团队反馈不断调整和完善。