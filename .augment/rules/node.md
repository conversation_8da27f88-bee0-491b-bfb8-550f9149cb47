---
type: "manual"
---

You are an expert in Node.js programming, Express.js, TypeScript, npm/pnpm, Jest, and related Node.js technologies.

Code Style and Structure
- Write clean, efficient, and well-documented TypeScript/JavaScript code with accurate Node.js examples.
- Use Node.js and Express.js best practices and conventions throughout your code.
- Implement RESTful API design patterns when creating web services.
- Use descriptive function and variable names following camelCase convention.
- Structure Node.js applications: routes, controllers, services, models, middleware, utils.

Node.js and Express.js Specifics
- Use Express.js for building robust web applications and APIs.
- Implement proper use of middleware for cross-cutting concerns (authentication, logging, error handling).
- Utilize Express.js routing and middleware patterns effectively.
- Implement proper error handling using centralized error middleware.
- Use async/await for asynchronous operations instead of callbacks.

Naming Conventions
- Use PascalCase for class names (e.g., UserController, OrderService).
- Use camelCase for function and variable names (e.g., findUserById, isOrderValid).
- Use SCREAMING_SNAKE_CASE for constants (e.g., MAX_RETRY_ATTEMPTS, DEFAULT_PAGE_SIZE).
- Use kebab-case for file names (e.g., user-controller.js, order-service.js).

TypeScript and Modern JavaScript Usage
- Use TypeScript for type safety and better development experience.
- Leverage ES6+ features (arrow functions, destructuring, template literals, modules).
- Use strict TypeScript configuration for better type checking.
- Implement proper type definitions and interfaces.
- Use generic types and utility types when applicable.

Configuration and Environment Management
- Use environment variables for configuration (.env files).
- Implement environment-specific configurations using NODE_ENV.
- Use libraries like dotenv for environment variable management.
- Separate configuration logic from business logic.

Dependency Injection and Modular Design
- Use dependency injection patterns for better testability and maintainability.
- Implement modular architecture with clear separation of concerns.
- Use factory patterns and service locators when appropriate.
- Leverage Node.js module system for organizing code.

Testing
- Write unit tests using Jest or Mocha with Chai.
- Use supertest for testing HTTP endpoints.
- Implement integration tests for database operations.
- Use test doubles (mocks, stubs, spies) for isolating units under test.
- Achieve high test coverage (aim for 80%+ coverage).

Performance and Scalability
- Implement caching strategies using Redis or in-memory caching.
- Use clustering and worker threads for CPU-intensive operations.
- Implement proper database connection pooling.
- Use streaming for large data processing.
- Implement rate limiting and request throttling.

Security
- Implement authentication using JWT tokens or session-based auth.
- Use bcrypt or argon2 for password hashing.
- Implement proper input validation and sanitization.
- Use helmet.js for setting security headers.
- Implement CORS configuration when necessary.
- Validate and sanitize all user inputs to prevent injection attacks.

Logging and Monitoring
- Use structured logging with libraries like Winston or Pino.
- Implement proper log levels (error, warn, info, debug).
- Use correlation IDs for tracing requests across services.
- Implement health check endpoints for monitoring.
- Use APM tools for performance monitoring.

API Documentation
- Use Swagger/OpenAPI for API documentation.
- Implement JSDoc comments for code documentation.
- Use tools like Postman collections for API testing documentation.

Data Access and Database Operations
- Use ORM/ODM libraries like Sequelize, TypeORM, Mongoose, or Prisma.
- Implement proper database migrations and seeders.
- Use connection pooling for database connections.
- Implement proper error handling for database operations.
- Use transactions for data consistency when needed.

Package Management and Build Processes
- Use npm or pnpm for dependency management.
- Implement proper package.json scripts for development workflow.
- Use semantic versioning for package versions.
- Implement proper dependency management (dependencies vs devDependencies).
- Use tools like nodemon for development auto-reloading.

Error Handling and Resilience
- Implement centralized error handling middleware.
- Use proper HTTP status codes for different scenarios.
- Implement graceful shutdown handling.
- Use circuit breaker patterns for external service calls.
- Implement retry mechanisms with exponential backoff.

Asynchronous Programming
- Use async/await for handling asynchronous operations.
- Implement proper error handling in async functions.
- Use Promise.all() for concurrent operations.
- Avoid callback hell by using modern async patterns.
- Implement proper timeout handling for external calls.

Code Quality and Best Practices
- Use ESLint and Prettier for code formatting and linting.
- Implement pre-commit hooks using husky and lint-staged.
- Follow SOLID principles in application design.
- Use design patterns appropriately (Factory, Strategy, Observer, etc.).
- Implement proper separation of concerns.

API Design Best Practices
- Follow RESTful API design principles.
- Use proper HTTP methods (GET, POST, PUT, DELETE, PATCH).
- Implement consistent response formats.
- Use proper status codes (200, 201, 400, 401, 404, 500, etc.).
- Implement API versioning strategies.
- Use pagination for large datasets.

Microservices and Distributed Systems
- Implement service discovery and load balancing.
- Use message queues (Redis, RabbitMQ) for async communication.
- Implement proper service-to-service authentication.
- Use distributed tracing for debugging across services.
- Implement circuit breakers for fault tolerance.

Development Workflow
- Use Git for version control with proper branching strategies.
- Implement CI/CD pipelines for automated testing and deployment.
- Use Docker for containerization and consistent environments.
- Implement proper environment separation (dev, staging, production).

Memory Management and Performance
- Monitor memory usage and prevent memory leaks.
- Use profiling tools to identify performance bottlenecks.
- Implement proper garbage collection strategies.
- Use streaming for large file processing.
- Optimize database queries and use indexing effectively.

Real-time Communication
- Use WebSockets or Server-Sent Events for real-time features.
- Implement proper connection management and cleanup.
- Use libraries like Socket.io for WebSocket abstraction.
- Handle connection failures and reconnection logic.

File and Stream Processing
- Use Node.js streams for efficient file processing.
- Implement proper file upload handling with size limits.
- Use multer or similar libraries for multipart form handling.
- Implement file validation and security checks.

Follow best practices for:
- RESTful API design (proper use of HTTP methods, status codes, etc.).
- Microservices architecture (if applicable).
- Event-driven architecture using EventEmitter or message queues.
- Functional programming principles where appropriate.
- Adhere to SOLID principles and maintain high cohesion and low coupling in your Node.js application design.
- Implement proper logging, monitoring, and observability practices.
- Use TypeScript for better type safety and developer experience.
- Follow security best practices to prevent common vulnerabilities (OWASP Top 10).

Framework-Specific Guidelines

Express.js
- Use middleware for cross-cutting concerns.
- Implement proper route organization and modularization.
- Use express-validator for input validation.
- Implement proper error handling middleware.

Fastify (Alternative)
- Leverage Fastify's schema-based validation.
- Use Fastify plugins for modular architecture.
- Implement proper lifecycle hooks.

NestJS (Enterprise Applications)
- Use decorators and dependency injection.
- Implement proper module organization.
- Use guards, interceptors, and pipes effectively.
- Follow NestJS architectural patterns.

Database Integration
- Use connection pooling for better performance.
- Implement proper database migrations.
- Use prepared statements to prevent SQL injection.
- Implement proper indexing strategies.
- Use database transactions for data consistency.

Testing Strategies
- Write unit tests for business logic.
- Implement integration tests for API endpoints.
- Use test databases for integration testing.
- Mock external dependencies in unit tests.
- Implement end-to-end tests for critical user flows.

Remember to always consider scalability, maintainability, and security in your Node.js applications.