# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=stock_db
DB_USER=postgres
DB_PASSWORD=your_password
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20

# 日志配置
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/stock_db.log
LOG_ROTATION=1 day
LOG_RETENTION=30 days

# AkShare配置
AKSHARE_TIMEOUT=30
AKSHARE_RETRY_TIMES=3
AKSHARE_RETRY_DELAY=1

# 数据更新配置
UPDATE_SCHEDULE_TIME=18:00
UPDATE_BATCH_SIZE=100
UPDATE_CONCURRENT_LIMIT=5

# Redis配置（可选，用于Celery）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=

# 应用配置
APP_NAME=stock-db
APP_VERSION=1.0.0
DEBUG=False
TIMEZONE=Asia/Shanghai
