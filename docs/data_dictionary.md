# 数据字典

## 概述

本文档详细描述了股票数据管理系统中所有数据表的结构、字段含义和约束条件。

## 数据表概览

| 表名 | 中文名称 | 用途 | 记录数量级 |
|------|----------|------|------------|
| stock_info | 股票基本信息表 | 存储股票基本信息 | 数千条 |
| stock_data | 股票数据表 | 存储历史行情数据 | 数千万条 |
| update_log | 数据更新日志表 | 记录更新历史 | 数万条 |

## 详细字段说明

### 1. stock_info (股票基本信息表)

**表用途**: 存储A股市场所有股票的基本信息，作为主表提供股票元数据。

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 中文名称 | 说明 | 示例值 |
|--------|----------|------|------|--------|----------|------|--------|
| id | BIGSERIAL | - | PRIMARY KEY | 自增 | 主键ID | 自增主键，唯一标识 | 1 |
| symbol | VARCHAR | 20 | NOT NULL, UNIQUE | - | 股票代码 | 标准股票代码，包含市场后缀 | 000001.SZ |
| name | VARCHAR | 100 | NOT NULL | - | 股票名称 | 股票中文简称 | 平安银行 |
| market | VARCHAR | 10 | NOT NULL | - | 市场类型 | 交易所标识 | SZ, SH, BJ |
| industry | VARCHAR | 50 | NULL | - | 所属行业 | 证监会行业分类 | 银行业 |
| sector | VARCHAR | 50 | NULL | - | 所属板块 | 概念板块分类 | 金融板块 |
| list_date | DATE | - | NULL | - | 上市日期 | 首次公开发行日期 | 1991-04-03 |
| delist_date | DATE | - | NULL | - | 退市日期 | 如果已退市的日期 | NULL |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否活跃 | 是否正常交易 | TRUE |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | 记录创建时间 | 2024-01-01 10:00:00 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | 记录最后更新时间 | 2024-01-01 10:00:00 |

**索引说明**:
- `idx_stock_symbol`: 股票代码唯一索引，用于快速查找
- `idx_stock_market`: 市场类型索引，用于按市场筛选
- `idx_stock_active`: 活跃状态索引，用于筛选正常交易股票

**业务规则**:
- 股票代码必须包含市场后缀（.SZ/.SH/.BJ）
- 退市股票的 `is_active` 应设为 FALSE
- 上市日期不能晚于当前日期

### 2. stock_data (股票数据表)

**表用途**: 存储股票的历史行情数据，支持多时间级别的K线数据。

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 中文名称 | 说明 | 示例值 |
|--------|----------|------|------|--------|----------|------|--------|
| id | BIGSERIAL | - | PRIMARY KEY | 自增 | 主键ID | 自增主键，唯一标识 | 1 |
| stock_id | BIGINT | - | NOT NULL, FK | - | 股票ID | 关联stock_info.id | 1 |
| symbol | VARCHAR | 20 | NOT NULL | - | 股票代码 | 冗余字段，提高查询效率 | 000001.SZ |
| period | VARCHAR | 10 | NOT NULL | - | 时间周期 | K线周期类型 | 1d, 1h, 5m |
| trade_date | DATE | - | NOT NULL | - | 交易日期 | 交易发生的日期 | 2024-01-15 |
| trade_time | TIMESTAMP | - | NOT NULL | - | 交易时间 | 精确的交易时间 | 2024-01-15 15:00:00 |
| open_price | DECIMAL | 10,3 | NOT NULL | - | 开盘价 | 开盘价格（元） | 12.350 |
| high_price | DECIMAL | 10,3 | NOT NULL | - | 最高价 | 最高价格（元） | 12.580 |
| low_price | DECIMAL | 10,3 | NOT NULL | - | 最低价 | 最低价格（元） | 12.200 |
| close_price | DECIMAL | 10,3 | NOT NULL | - | 收盘价 | 收盘价格（元） | 12.450 |
| volume | BIGINT | - | NOT NULL | - | 成交量 | 成交股数 | 1234567 |
| amount | DECIMAL | 15,2 | NOT NULL | - | 成交额 | 成交金额（元） | 15432100.50 |
| turnover_rate | DECIMAL | 8,4 | NULL | - | 换手率 | 换手率百分比 | 1.2500 |
| price_change | DECIMAL | 10,3 | NULL | - | 价格变动 | 相对前一周期的价格变动 | 0.100 |
| pct_change | DECIMAL | 8,4 | NULL | - | 涨跌幅 | 涨跌幅百分比 | 0.8100 |
| created_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 创建时间 | 记录创建时间 | 2024-01-01 10:00:00 |
| updated_at | TIMESTAMP | - | NOT NULL | CURRENT_TIMESTAMP | 更新时间 | 记录最后更新时间 | 2024-01-01 10:00:00 |

**索引说明**:
- `uq_stock_data_unique`: 唯一约束，防止重复数据
- `idx_stock_data_symbol_period`: 复合索引，用于按股票和周期查询
- `idx_stock_data_date`: 交易日期索引，用于时间范围查询
- `idx_stock_data_time`: 交易时间索引，用于精确时间查询
- `idx_stock_data_symbol_date`: 复合索引，用于单股票时间查询

**业务规则**:
- 所有价格字段必须大于0
- 最高价 >= 最低价
- 最高价 >= max(开盘价, 收盘价)
- 最低价 <= min(开盘价, 收盘价)
- 成交量和成交额必须 >= 0
- 同一股票、同一周期、同一时间只能有一条记录

**周期类型说明**:
| 周期代码 | 中文名称 | 时间间隔 | 数据特点 |
|----------|----------|----------|----------|
| 1m | 1分钟线 | 1分钟 | 数据量大，实时性强 |
| 5m | 5分钟线 | 5分钟 | 短期交易分析 |
| 30m | 30分钟线 | 30分钟 | 日内趋势分析 |
| 1h | 1小时线 | 1小时 | 中短期趋势 |
| 1d | 日线 | 1天 | 最常用的分析周期 |
| 1w | 周线 | 1周 | 中期趋势分析 |
| 1M | 月线 | 1个月 | 长期趋势分析 |
| 1y | 年线 | 1年 | 超长期分析 |

### 3. update_log (数据更新日志表)

**表用途**: 记录数据更新任务的执行历史，用于监控和调试。

| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 中文名称 | 说明 | 示例值 |
|--------|----------|------|------|--------|----------|------|--------|
| id | BIGSERIAL | - | PRIMARY KEY | 自增 | 主键ID | 自增主键，唯一标识 | 1 |
| task_type | VARCHAR | 50 | NOT NULL | - | 任务类型 | 更新任务的类型 | stock_data |
| symbol | VARCHAR | 20 | NULL | - | 股票代码 | 如果是单股票更新 | 000001.SZ |
| period | VARCHAR | 10 | NULL | - | 时间周期 | 如果是股票数据更新 | 1d |
| start_date | DATE | - | NULL | - | 开始日期 | 更新数据的开始日期 | 2024-01-01 |
| end_date | DATE | - | NULL | - | 结束日期 | 更新数据的结束日期 | 2024-01-15 |
| total_records | INTEGER | - | NOT NULL | 0 | 总记录数 | 计划处理的记录数 | 1000 |
| success_records | INTEGER | - | NOT NULL | 0 | 成功记录数 | 成功处理的记录数 | 950 |
| failed_records | INTEGER | - | NOT NULL | 0 | 失败记录数 | 处理失败的记录数 | 50 |
| status | VARCHAR | 20 | NOT NULL | - | 执行状态 | 任务执行状态 | success |
| error_message | TEXT | - | NULL | - | 错误信息 | 如果失败的错误详情 | Connection timeout |
| start_time | TIMESTAMP | - | NOT NULL | - | 开始时间 | 任务开始执行时间 | 2024-01-01 18:00:00 |
| end_time | TIMESTAMP | - | NULL | - | 结束时间 | 任务结束时间 | 2024-01-01 18:30:00 |
| duration_seconds | INTEGER | - | NULL | - | 执行时长 | 任务执行时长（秒） | 1800 |

**索引说明**:
- `idx_update_log_type`: 任务类型索引
- `idx_update_log_symbol`: 股票代码索引
- `idx_update_log_status`: 执行状态索引
- `idx_update_log_time`: 开始时间索引

**状态类型说明**:
| 状态值 | 中文名称 | 说明 |
|--------|----------|------|
| running | 运行中 | 任务正在执行 |
| success | 成功 | 任务完全成功 |
| failed | 失败 | 任务完全失败 |
| partial | 部分成功 | 任务部分成功，部分失败 |

**任务类型说明**:
| 任务类型 | 中文名称 | 说明 |
|----------|----------|------|
| stock_info | 股票信息更新 | 更新股票基本信息 |
| stock_data | 股票数据更新 | 更新股票行情数据 |
| stock_data_single | 单股票更新 | 更新单只股票数据 |
| stock_data_batch | 批量更新 | 批量更新多只股票 |

## 数据关系

### 主要关联关系

1. **stock_info → stock_data**: 一对多关系
   - 一只股票对应多条历史数据
   - 通过 `stock_data.stock_id` 关联 `stock_info.id`

2. **数据完整性约束**:
   - 外键约束确保数据一致性
   - 唯一约束防止重复数据
   - 检查约束确保数据有效性

### 查询优化建议

1. **常用查询模式**:
   ```sql
   -- 查询单股票时间序列数据
   SELECT * FROM stock_data 
   WHERE symbol = '000001.SZ' AND period = '1d' 
   ORDER BY trade_date DESC;
   
   -- 查询某日所有股票收盘价
   SELECT symbol, close_price FROM stock_data 
   WHERE period = '1d' AND trade_date = '2024-01-15';
   ```

2. **索引使用**:
   - 时间范围查询使用 `trade_date` 索引
   - 单股票查询使用 `symbol` 索引
   - 复合查询使用复合索引

3. **分区建议**:
   - 按时间分区提高查询性能
   - 按周期类型分区减少扫描范围

## 数据质量控制

### 1. 数据验证规则

- **价格数据**: 必须为正数，符合价格逻辑关系
- **成交数据**: 成交量和成交额必须非负
- **时间数据**: 交易时间必须在合理范围内
- **重复检查**: 防止同一时间点的重复数据

### 2. 数据清洗

- **异常值检测**: 识别价格异常波动
- **缺失值处理**: 处理数据源缺失情况
- **格式标准化**: 统一数据格式和精度

### 3. 监控指标

- **数据完整性**: 检查数据缺失情况
- **数据及时性**: 监控数据更新延迟
- **数据准确性**: 对比多个数据源验证

## 存储估算

### 数据量预估

| 表名 | 单条记录大小 | 预估记录数 | 总存储空间 |
|------|--------------|------------|------------|
| stock_info | ~200 字节 | 5,000 | ~1 MB |
| stock_data | ~100 字节 | 100,000,000 | ~10 GB |
| update_log | ~300 字节 | 100,000 | ~30 MB |

### 增长预测

- **日线数据**: 每日新增约 5,000 条记录
- **分钟数据**: 每日新增约 1,200,000 条记录
- **年增长**: 约 4-5 GB/年（包含所有周期）

### 存储优化

- **历史数据归档**: 将老旧分钟数据迁移到冷存储
- **数据压缩**: 使用数据库压缩功能
- **分区管理**: 定期维护分区表
