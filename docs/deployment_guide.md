# 部署指南

## 概述

本文档详细说明如何在生产环境中部署股票数据管理系统。

## 系统要求

### 硬件要求

| 组件 | 最低配置 | 推荐配置 | 说明 |
|------|----------|----------|------|
| CPU | 2核 | 4核+ | 支持并发数据处理 |
| 内存 | 4GB | 8GB+ | 数据处理和缓存 |
| 存储 | 50GB | 200GB+ | 数据库和日志存储 |
| 网络 | 10Mbps | 100Mbps+ | 数据获取带宽 |

### 软件要求

| 软件 | 版本要求 | 说明 |
|------|----------|------|
| Python | 3.9+ | 主要运行环境 |
| PostgreSQL | 12+ | 数据库服务 |
| Redis | 6.0+ | 可选，用于Celery |
| Git | 2.0+ | 代码管理 |

## 部署方式

### 方式一：传统部署

#### 1. 环境准备

```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Python和依赖
sudo apt install python3.9 python3.9-venv python3.9-dev -y
sudo apt install postgresql postgresql-contrib -y
sudo apt install git curl wget -y

# 安装PostgreSQL开发库
sudo apt install libpq-dev -y
```

#### 2. 数据库配置

```bash
# 启动PostgreSQL服务
sudo systemctl start postgresql
sudo systemctl enable postgresql

# 创建数据库用户和数据库
sudo -u postgres psql << EOF
CREATE USER stock_user WITH PASSWORD 'your_secure_password';
CREATE DATABASE stock_db OWNER stock_user;
GRANT ALL PRIVILEGES ON DATABASE stock_db TO stock_user;
\q
EOF

# 配置PostgreSQL连接
sudo nano /etc/postgresql/*/main/postgresql.conf
# 修改: listen_addresses = 'localhost'

sudo nano /etc/postgresql/*/main/pg_hba.conf
# 添加: local   stock_db   stock_user   md5

# 重启PostgreSQL
sudo systemctl restart postgresql
```

#### 3. 应用部署

```bash
# 创建应用用户
sudo useradd -m -s /bin/bash stock_app
sudo su - stock_app

# 克隆代码
git clone <repository-url> stock-db
cd stock-db

# 创建虚拟环境
python3.9 -m venv venv
source venv/bin/activate

# 安装依赖
pip install --upgrade pip
pip install -e .

# 配置环境变量
cp .env.example .env
nano .env
# 修改数据库连接信息

# 初始化数据库
stock-db init-db

# 测试连接
stock-db check-db
```

#### 4. 系统服务配置

创建systemd服务文件：

```bash
sudo nano /etc/systemd/system/stock-db-update.service
```

```ini
[Unit]
Description=Stock DB Daily Update Service
After=network.target postgresql.service

[Service]
Type=oneshot
User=stock_app
Group=stock_app
WorkingDirectory=/home/<USER>/stock-db
Environment=PATH=/home/<USER>/stock-db/venv/bin
ExecStart=/home/<USER>/stock-db/venv/bin/python scripts/daily_update.py
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
```

创建定时器：

```bash
sudo nano /etc/systemd/system/stock-db-update.timer
```

```ini
[Unit]
Description=Run Stock DB Update Daily
Requires=stock-db-update.service

[Timer]
OnCalendar=Mon-Fri 18:00
Persistent=true

[Install]
WantedBy=timers.target
```

启用服务：

```bash
sudo systemctl daemon-reload
sudo systemctl enable stock-db-update.timer
sudo systemctl start stock-db-update.timer
```

### 方式二：Docker部署

#### 1. 创建Dockerfile

```dockerfile
FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .
COPY pyproject.toml .

# 安装Python依赖
RUN pip install --no-cache-dir -e .

# 复制应用代码
COPY src/ src/
COPY scripts/ scripts/

# 创建日志目录
RUN mkdir -p logs

# 设置环境变量
ENV PYTHONPATH=/app/src

# 暴露端口（如果需要）
EXPOSE 8000

# 默认命令
CMD ["python", "scripts/daily_update.py"]
```

#### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: stock_db
      POSTGRES_USER: stock_user
      POSTGRES_PASSWORD: your_secure_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  stock-db:
    build: .
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_NAME: stock_db
      DB_USER: stock_user
      DB_PASSWORD: your_secure_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
    volumes:
      - ./logs:/app/logs
      - ./.env:/app/.env
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

volumes:
  postgres_data:
```

#### 3. 部署命令

```bash
# 构建和启动服务
docker-compose up -d

# 初始化数据库
docker-compose exec stock-db stock-db init-db

# 查看日志
docker-compose logs -f stock-db
```

### 方式三：Kubernetes部署

#### 1. 创建配置文件

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: stock-db

---
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: stock-db-config
  namespace: stock-db
data:
  DB_HOST: postgres-service
  DB_PORT: "5432"
  DB_NAME: stock_db
  REDIS_HOST: redis-service
  REDIS_PORT: "6379"

---
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: stock-db-secret
  namespace: stock-db
type: Opaque
data:
  DB_USER: c3RvY2tfdXNlcg==  # base64 encoded
  DB_PASSWORD: eW91cl9zZWN1cmVfcGFzc3dvcmQ=  # base64 encoded

---
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: stock-db
  namespace: stock-db
spec:
  replicas: 1
  selector:
    matchLabels:
      app: stock-db
  template:
    metadata:
      labels:
        app: stock-db
    spec:
      containers:
      - name: stock-db
        image: stock-db:latest
        envFrom:
        - configMapRef:
            name: stock-db-config
        - secretRef:
            name: stock-db-secret
        volumeMounts:
        - name: logs
          mountPath: /app/logs
      volumes:
      - name: logs
        persistentVolumeClaim:
          claimName: stock-db-logs-pvc

---
# cronjob.yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: stock-db-daily-update
  namespace: stock-db
spec:
  schedule: "0 18 * * 1-5"  # 工作日18:00
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: daily-update
            image: stock-db:latest
            command: ["python", "scripts/daily_update.py"]
            envFrom:
            - configMapRef:
                name: stock-db-config
            - secretRef:
                name: stock-db-secret
          restartPolicy: OnFailure
```

## 监控和日志

### 1. 日志配置

```bash
# 配置logrotate
sudo nano /etc/logrotate.d/stock-db
```

```
/home/<USER>/stock-db/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 stock_app stock_app
    postrotate
        systemctl reload stock-db-update || true
    endscript
}
```

### 2. 监控脚本

```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

LOG_FILE="/home/<USER>/stock-db/logs/monitor.log"
DB_CHECK=$(stock-db check-db 2>&1)

echo "$(date): 开始系统检查" >> $LOG_FILE

# 检查数据库连接
if echo "$DB_CHECK" | grep -q "数据库连接正常"; then
    echo "$(date): 数据库连接正常" >> $LOG_FILE
else
    echo "$(date): 数据库连接异常: $DB_CHECK" >> $LOG_FILE
    # 发送告警邮件
    echo "数据库连接异常" | mail -s "Stock-DB Alert" <EMAIL>
fi

# 检查磁盘空间
DISK_USAGE=$(df -h /home/<USER>/stock-db | awk 'NR==2 {print $5}' | sed 's/%//')
if [ $DISK_USAGE -gt 80 ]; then
    echo "$(date): 磁盘空间不足: ${DISK_USAGE}%" >> $LOG_FILE
    echo "磁盘空间不足: ${DISK_USAGE}%" | mail -s "Stock-DB Disk Alert" <EMAIL>
fi

# 检查最近更新状态
LAST_UPDATE=$(stock-db stats | grep "最新日期" | awk '{print $2}')
TODAY=$(date +%Y-%m-%d)
if [ "$LAST_UPDATE" != "$TODAY" ]; then
    echo "$(date): 数据更新可能延迟，最新数据: $LAST_UPDATE" >> $LOG_FILE
fi

echo "$(date): 系统检查完成" >> $LOG_FILE
```

### 3. 性能监控

```python
# performance_monitor.py
import psutil
import time
from stock_db.database import get_db_context
from stock_db.utils.logger import get_logger

logger = get_logger(__name__)

def monitor_system():
    """系统性能监控"""
    
    # CPU使用率
    cpu_percent = psutil.cpu_percent(interval=1)
    
    # 内存使用率
    memory = psutil.virtual_memory()
    memory_percent = memory.percent
    
    # 磁盘使用率
    disk = psutil.disk_usage('/')
    disk_percent = (disk.used / disk.total) * 100
    
    # 数据库连接数
    with get_db_context() as session:
        result = session.execute(
            "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
        ).scalar()
        active_connections = result
    
    logger.info(f"系统监控 - CPU: {cpu_percent}%, 内存: {memory_percent}%, "
               f"磁盘: {disk_percent:.1f}%, 数据库连接: {active_connections}")
    
    # 告警阈值
    if cpu_percent > 80:
        logger.warning(f"CPU使用率过高: {cpu_percent}%")
    if memory_percent > 80:
        logger.warning(f"内存使用率过高: {memory_percent}%")
    if disk_percent > 80:
        logger.warning(f"磁盘使用率过高: {disk_percent:.1f}%")

if __name__ == "__main__":
    monitor_system()
```

## 备份和恢复

### 1. 数据库备份

```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

BACKUP_DIR="/backup/stock-db"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="stock_db"
DB_USER="stock_user"

mkdir -p $BACKUP_DIR

# 全量备份
pg_dump -h localhost -U $DB_USER -d $DB_NAME -f $BACKUP_DIR/stock_db_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/stock_db_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "备份完成: stock_db_$DATE.sql.gz"
```

### 2. 数据恢复

```bash
#!/bin/bash
# restore.sh - 数据库恢复脚本

BACKUP_FILE=$1
DB_NAME="stock_db"
DB_USER="stock_user"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file>"
    exit 1
fi

# 解压备份文件
if [[ $BACKUP_FILE == *.gz ]]; then
    gunzip -c $BACKUP_FILE > /tmp/restore.sql
    RESTORE_FILE="/tmp/restore.sql"
else
    RESTORE_FILE=$BACKUP_FILE
fi

# 恢复数据库
psql -h localhost -U $DB_USER -d $DB_NAME -f $RESTORE_FILE

echo "数据恢复完成"
```

## 安全配置

### 1. 数据库安全

```sql
-- 创建只读用户
CREATE USER readonly_user WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE stock_db TO readonly_user;
GRANT USAGE ON SCHEMA public TO readonly_user;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO readonly_user;

-- 设置连接限制
ALTER USER stock_user CONNECTION LIMIT 10;
```

### 2. 系统安全

```bash
# 设置文件权限
chmod 600 .env
chmod 700 scripts/
chown -R stock_app:stock_app /home/<USER>/stock-db

# 配置防火墙
sudo ufw allow 22/tcp
sudo ufw allow from localhost to any port 5432
sudo ufw enable
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务状态
   - 验证连接参数
   - 检查防火墙设置

2. **数据更新失败**
   - 检查网络连接
   - 验证AkShare API状态
   - 查看错误日志

3. **性能问题**
   - 检查数据库索引
   - 监控系统资源
   - 优化查询语句

### 日志分析

```bash
# 查看应用日志
tail -f logs/stock_db.log

# 查看数据库日志
sudo tail -f /var/log/postgresql/postgresql-*.log

# 查看系统日志
journalctl -u stock-db-update.service -f
```

## 维护计划

### 日常维护

- 检查系统状态和日志
- 监控磁盘空间使用
- 验证数据更新状态

### 周期维护

- 数据库性能分析
- 清理过期日志文件
- 更新系统和依赖包

### 年度维护

- 归档历史数据
- 系统安全审计
- 灾难恢复演练
