# 快速开始指南

## 5分钟快速体验

### 1. 环境准备

确保您的系统已安装：
- Python 3.9+
- PostgreSQL 12+
- Git

### 2. 克隆项目

```bash
git clone <repository-url>
cd stock-db
```

### 3. 创建Conda环境

```bash
# 创建并激活conda环境
conda env create -f environment.yml
conda activate stock-db
```

### 4. 配置数据库

编辑 `.env` 文件：

```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=stock_db
DB_USER=your_username
DB_PASSWORD=your_password
```

### 5. 初始化数据

```bash
# 检查数据库连接
python scripts/run.py check-db

# 更新股票列表
python scripts/run.py update-stocks

# 获取示例数据（平安银行日线数据）
python scripts/run.py update-data --symbol 000001.SZ --periods 1d
```

### 6. 查看数据

```bash
# 查询数据
python scripts/run.py query 000001.SZ --limit 5

# 查看统计信息
python scripts/run.py stats
```

## 常用命令

### 数据更新

```bash
# 增量更新（推荐日常使用）
python scripts/run.py update-data --incremental

# 更新特定股票
python scripts/run.py update-data --symbol 000001.SZ --periods 1d 1h

# 批量更新所有股票
python scripts/run.py update-data --periods 1d --force
```

### 数据查询

```bash
# 查询股票数据
python scripts/run.py query 000001.SZ --period 1d --limit 10

# 查询指定时间范围
python scripts/run.py query 000001.SZ --start-date 2024-01-01 --end-date 2024-01-31
```

### 定时任务

```bash
# 前台运行定时任务
python scripts/run.py schedule --time 18:00

# 设置系统定时任务
chmod +x scripts/setup_cron.sh
./scripts/setup_cron.sh
```

## Python API 使用

```python
from src import StockDataService, UpdateService

# 创建服务
stock_service = StockDataService()
update_service = UpdateService()

# 获取数据
data_list = stock_service.get_stock_data(
    symbol="000001.SZ",
    period="1d",
    limit=10
)

for data in data_list:
    print(f"{data.trade_date}: {data.close_price}")

# 更新数据
result = update_service.update_single_stock(
    symbol="000001.SZ",
    periods=["1d"]
)
print(f"更新结果: {result}")
```

## 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查PostgreSQL服务
   sudo systemctl status postgresql

   # 检查连接参数
   python scripts/run.py check-db
   ```

2. **权限错误**
   ```bash
   # 确保数据库用户有正确权限
   sudo -u postgres psql
   GRANT ALL PRIVILEGES ON DATABASE stock_db TO your_username;
   ```

3. **依赖安装失败**
   ```bash
   # 更新pip
   pip install --upgrade pip
   
   # 安装系统依赖（Ubuntu/Debian）
   sudo apt install python3-dev libpq-dev
   ```

## 下一步

- 阅读 [完整文档](README.md)
- 查看 [API 参考](docs/api_reference.md)
- 了解 [部署指南](docs/deployment_guide.md)
- 参考 [数据字典](docs/data_dictionary.md)
