# 数据库设计文档

## 概述

本系统使用PostgreSQL数据库存储股票数据，支持多时间级别的历史行情数据。数据库设计遵循第三范式，确保数据一致性和查询效率。

## 数据库表结构

### 1. 股票基本信息表 (stock_info)

存储股票的基本信息，作为主表。

| 字段名 | 数据类型 | 约束 | 描述 |
|--------|----------|------|------|
| id | BIGSERIAL | PRIMARY KEY | 自增主键 |
| symbol | VARCHAR(20) | NOT NULL, UNIQUE | 股票代码（如：000001.SZ） |
| name | VARCHAR(100) | NOT NULL | 股票名称 |
| market | VARCHAR(10) | NOT NULL | 市场类型（SZ/SH/BJ） |
| industry | VARCHAR(50) | | 所属行业 |
| sector | VARCHAR(50) | | 所属板块 |
| list_date | DATE | | 上市日期 |
| delist_date | DATE | | 退市日期（如果已退市） |
| is_active | BOOLEAN | DEFAULT TRUE | 是否活跃交易 |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE INDEX idx_stock_symbol (symbol)
- INDEX idx_stock_market (market)
- INDEX idx_stock_active (is_active)

### 2. 股票数据表 (stock_data)

存储股票的历史行情数据，支持多时间级别。

| 字段名 | 数据类型 | 约束 | 描述 |
|--------|----------|------|------|
| id | BIGSERIAL | PRIMARY KEY | 自增主键 |
| stock_id | BIGINT | NOT NULL, FOREIGN KEY | 股票ID（关联stock_info.id） |
| symbol | VARCHAR(20) | NOT NULL | 股票代码（冗余字段，提高查询效率） |
| period | VARCHAR(10) | NOT NULL | 时间周期（1m/5m/30m/1h/1d/1w/1M/1y） |
| trade_date | DATE | NOT NULL | 交易日期 |
| trade_time | TIMESTAMP | NOT NULL | 交易时间（精确到分钟） |
| open_price | DECIMAL(10,3) | NOT NULL | 开盘价 |
| high_price | DECIMAL(10,3) | NOT NULL | 最高价 |
| low_price | DECIMAL(10,3) | NOT NULL | 最低价 |
| close_price | DECIMAL(10,3) | NOT NULL | 收盘价 |
| volume | BIGINT | NOT NULL | 成交量（股） |
| amount | DECIMAL(15,2) | NOT NULL | 成交额（元） |
| turnover_rate | DECIMAL(8,4) | | 换手率（%） |
| price_change | DECIMAL(10,3) | | 价格变动 |
| pct_change | DECIMAL(8,4) | | 涨跌幅（%） |
| created_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | DEFAULT CURRENT_TIMESTAMP | 更新时间 |

**索引：**
- PRIMARY KEY (id)
- UNIQUE INDEX idx_stock_data_unique (stock_id, period, trade_time)
- INDEX idx_stock_data_symbol_period (symbol, period)
- INDEX idx_stock_data_date (trade_date)
- INDEX idx_stock_data_time (trade_time)
- INDEX idx_stock_data_symbol_date (symbol, trade_date)

### 3. 数据更新日志表 (update_log)

记录数据更新的历史记录，便于追踪和调试。

| 字段名 | 数据类型 | 约束 | 描述 |
|--------|----------|------|------|
| id | BIGSERIAL | PRIMARY KEY | 自增主键 |
| task_type | VARCHAR(50) | NOT NULL | 任务类型（stock_info/stock_data） |
| symbol | VARCHAR(20) | | 股票代码（如果是股票数据更新） |
| period | VARCHAR(10) | | 时间周期（如果是股票数据更新） |
| start_date | DATE | | 更新开始日期 |
| end_date | DATE | | 更新结束日期 |
| total_records | INTEGER | DEFAULT 0 | 总记录数 |
| success_records | INTEGER | DEFAULT 0 | 成功记录数 |
| failed_records | INTEGER | DEFAULT 0 | 失败记录数 |
| status | VARCHAR(20) | NOT NULL | 状态（running/success/failed/partial） |
| error_message | TEXT | | 错误信息 |
| start_time | TIMESTAMP | NOT NULL | 开始时间 |
| end_time | TIMESTAMP | | 结束时间 |
| duration_seconds | INTEGER | | 执行时长（秒） |

**索引：**
- PRIMARY KEY (id)
- INDEX idx_update_log_type (task_type)
- INDEX idx_update_log_symbol (symbol)
- INDEX idx_update_log_status (status)
- INDEX idx_update_log_time (start_time)

## 时间周期定义

| 周期代码 | 描述 | 数据来源 | 更新频率 |
|----------|------|----------|----------|
| 1m | 1分钟 | AkShare | 实时（交易时间） |
| 5m | 5分钟 | AkShare | 实时（交易时间） |
| 30m | 30分钟 | AkShare | 实时（交易时间） |
| 1h | 1小时 | AkShare | 实时（交易时间） |
| 1d | 日线 | AkShare | 每日收盘后 |
| 1w | 周线 | AkShare | 每周末 |
| 1M | 月线 | AkShare | 每月末 |
| 1y | 年线 | AkShare | 每年末 |

## 数据分区策略

为了提高查询性能，建议对stock_data表进行分区：

### 按时间分区
```sql
-- 按年分区（适用于日线及以上级别数据）
CREATE TABLE stock_data_2024 PARTITION OF stock_data
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

-- 按月分区（适用于分钟级数据）
CREATE TABLE stock_data_202401 PARTITION OF stock_data
FOR VALUES FROM ('2024-01-01') TO ('2024-02-01');
```

### 按周期分区
```sql
-- 按周期类型分区
CREATE TABLE stock_data_minute PARTITION OF stock_data
FOR VALUES IN ('1m', '5m', '30m');

CREATE TABLE stock_data_hour PARTITION OF stock_data
FOR VALUES IN ('1h');

CREATE TABLE stock_data_daily PARTITION OF stock_data
FOR VALUES IN ('1d', '1w', '1M', '1y');
```

## 数据约束和触发器

### 数据约束
```sql
-- 价格约束（价格必须大于0）
ALTER TABLE stock_data ADD CONSTRAINT chk_positive_prices 
CHECK (open_price > 0 AND high_price > 0 AND low_price > 0 AND close_price > 0);

-- 价格逻辑约束（最高价>=最低价）
ALTER TABLE stock_data ADD CONSTRAINT chk_price_logic 
CHECK (high_price >= low_price);

-- 成交量约束（成交量必须>=0）
ALTER TABLE stock_data ADD CONSTRAINT chk_positive_volume 
CHECK (volume >= 0 AND amount >= 0);
```

### 自动更新时间戳
```sql
-- 创建更新时间戳函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表添加触发器
CREATE TRIGGER update_stock_info_updated_at 
    BEFORE UPDATE ON stock_info 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_stock_data_updated_at 
    BEFORE UPDATE ON stock_data 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

## 查询优化建议

### 1. 常用查询模式
```sql
-- 查询某股票某时间段的日线数据
SELECT * FROM stock_data 
WHERE symbol = '000001.SZ' 
  AND period = '1d' 
  AND trade_date BETWEEN '2024-01-01' AND '2024-12-31'
ORDER BY trade_date;

-- 查询某日所有股票的收盘价
SELECT symbol, close_price, volume 
FROM stock_data 
WHERE period = '1d' 
  AND trade_date = '2024-01-15'
ORDER BY symbol;
```

### 2. 索引使用建议
- 对于时间范围查询，使用trade_date索引
- 对于单股票查询，使用symbol索引
- 对于复合查询，使用复合索引

### 3. 分区表查询
- 查询时尽量包含分区键，避免全表扫描
- 使用EXPLAIN ANALYZE分析查询计划

## 数据备份策略

### 1. 定期备份
```bash
# 每日备份
pg_dump -h localhost -U postgres -d stock_db > backup_$(date +%Y%m%d).sql

# 增量备份（使用WAL）
pg_basebackup -h localhost -U postgres -D /backup/base -Ft -z -P
```

### 2. 数据归档
- 将历史数据（如1年前的分钟级数据）归档到冷存储
- 保留热数据在主数据库中以提高查询性能

## 监控指标

### 1. 数据质量监控
- 每日数据完整性检查
- 价格异常值检测
- 数据更新延迟监控

### 2. 性能监控
- 查询响应时间
- 数据库连接数
- 磁盘空间使用率
- 索引使用效率
