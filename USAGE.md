# 使用指南

## 🚀 快速开始

### 1. 安装
```bash
# 克隆项目
git clone <repository-url>
cd stock-db

# 一键安装
./scripts/install.sh
```

### 2. 基本使用
```bash
# 激活环境
conda activate stock-db

# 检查系统状态
python scripts/run.py check-db

# 更新股票列表
python scripts/run.py update-stocks

# 获取数据
python scripts/run.py update-data --symbol 000001.SZ --periods 1d

# 查询数据
python scripts/run.py query 000001.SZ --limit 5
```

## 📋 常用命令

### 数据库操作
```bash
python scripts/run.py init-db          # 初始化数据库
python scripts/run.py check-db         # 检查连接
python scripts/run.py stats            # 查看统计
```

### 数据更新
```bash
python scripts/run.py update-stocks                    # 更新股票列表
python scripts/run.py update-data --incremental        # 增量更新
python scripts/run.py update-data --symbol 000001.SZ   # 更新单股票
python scripts/run.py update-data --periods 1d 1h      # 更新多周期
```

### 数据查询
```bash
python scripts/run.py query 000001.SZ --period 1d --limit 10
python scripts/run.py query 000001.SZ --start-date 2024-01-01 --end-date 2024-01-31
```

### 定时任务
```bash
python scripts/run.py schedule --time 18:00    # 前台运行
./scripts/setup_cron.sh                        # 设置系统定时任务
```

## 🔧 配置

### 环境变量 (.env)
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=stock_db
DB_USER=your_username
DB_PASSWORD=your_password
```

### 支持的时间周期
- `1m` - 1分钟线
- `5m` - 5分钟线
- `30m` - 30分钟线
- `1h` - 1小时线
- `1d` - 日线
- `1w` - 周线
- `1M` - 月线
- `1y` - 年线

## 📊 项目结构

```
stock-db/
├── src/                    # 源代码
├── scripts/                # 所有脚本
│   ├── run.py             # 主启动脚本
│   ├── install.sh         # 安装脚本
│   ├── daily_update.py    # 每日更新
│   └── setup_cron.sh      # 定时任务设置
├── docs/                   # 文档
├── tests/                  # 测试
└── environment.yml         # Conda环境
```

## ❓ 常见问题

1. **数据库连接失败**
   - 检查PostgreSQL服务是否运行
   - 确认.env文件配置正确

2. **conda环境问题**
   - 重新创建环境：`conda env remove -n stock-db && conda env create -f environment.yml`

3. **权限错误**
   - 给脚本执行权限：`chmod +x scripts/*.sh`

更多详细信息请查看 [README.md](README.md) 和 [docs/](docs/) 目录。
